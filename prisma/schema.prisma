// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

// MongoDB - Primary Database
datasource db {
  provider = "mongodb"
  url      = env("MONGODB_URL")
}

// User model for MongoDB (Primary)
model User {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  email       String   @unique
  username    String   @unique
  password    String
  firstName   String?
  lastName    String?
  avatar      String?
  role        UserRole @default(USER)
  isActive    Boolean  @default(true)
  isVerified  Boolean  @default(false)
  lastLogin   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  sessions    Session[]
  profile     UserProfile?

  // Full text search index for user search
  @@fulltext([email, username, firstName, lastName])
  @@map("users")
}

model UserProfile {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String   @unique @db.ObjectId
  phone       String?
  address     String?
  dateOfBirth DateTime?
  gender      Gender?
  bio         String?
  website     String?
  socialLinks Json?
  preferences Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Full text search index for profile search
  @@fulltext([bio])
  @@map("user_profiles")
}

model Session {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  userId      String   @db.ObjectId
  token       String   @unique
  refreshToken String?
  expiresAt   DateTime
  isActive    Boolean  @default(true)
  userAgent   String?
  ipAddress   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}



// Enums
enum UserRole {
  ADMIN
  MODERATOR
  USER
  GUEST
}

enum Gender {
  MALE
  FEMALE
  OTHER
  PREFER_NOT_TO_SAY
}
