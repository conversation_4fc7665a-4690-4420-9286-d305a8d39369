
// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma-sql"
      previewFeatures = ["multiSchema"]
}

// PostgreSQL - Secondary Database
datasource db {
  provider = "mysql"
  url      = env("MYSQL_URL")
}

model Idtable1 {
    id                String    @id
    passwd            String?
    vip               Int?
    viptime           Int?
    point             Int?
    reg_date          DateTime? @default(now())
    userLevel         Int?
    char_name         String?
    gameserver_burnho Int?
    serverenter_time  DateTime?
    enter_ip          String?
    record_lock       Int?
    lock_time         Int?
    web_block         DateTime?
    game_block        DateTime?
    delete_flag       Int?
    delete_date       DateTime?
    pay_flag          Int?
    update_date       DateTime?
    nick_name         String?
    lovekey           String?
    loveauth          String?
    Mac               String?
    Status            Int?
    fcsaccountguid    Int?
    recommender       String?
    LoginDate         DateTime?
    LoginCount        Int?
    email             String?
    RegIp             Int?
    Cash              Int?
    HorCah            Int?
    Coin              Int?

    @@schema("seal_member")
}

model Idtable2 {
    id                String    @id
    passwd            String?
    vip               Int?
    viptime           Int?
    point             Int?
    reg_date          DateTime? @default(now())
    userLevel         Int?
    char_name         String?
    gameserver_burnho Int?
    serverenter_time  DateTime?
    enter_ip          String?
    record_lock       Int?
    lock_time         Int?
    web_block         DateTime?
    game_block        DateTime?
    delete_flag       Int?
    delete_date       DateTime?
    pay_flag          Int?
    update_date       DateTime?
    nick_name         String?
    lovekey           String?
    loveauth          String?
    Mac               String?
    Status            Int?
    fcsaccountguid    Int?
    recommender       String?
    LoginDate         DateTime?
    LoginCount        Int?
    email             String?
    RegIp             Int?
    Cash              Int?
    HorCah            Int?
    Coin              Int?

    @@schema("seal_member")
}

model Idtable3 {
    id                String    @id
    passwd            String?
    vip               Int?
    viptime           Int?
    point             Int?
    reg_date          DateTime? @default(now())
    userLevel         Int?
    char_name         String?
    gameserver_burnho Int?
    serverenter_time  DateTime?
    enter_ip          String?
    record_lock       Int?
    lock_time         Int?
    web_block         DateTime?
    game_block        DateTime?
    delete_flag       Int?
    delete_date       DateTime?
    pay_flag          Int?
    update_date       DateTime?
    nick_name         String?
    lovekey           String?
    loveauth          String?
    Mac               String?
    Status            Int?
    fcsaccountguid    Int?
    recommender       String?
    LoginDate         DateTime?
    LoginCount        Int?
    email             String?
    RegIp             Int?
    Cash              Int?
    HorCah            Int?
    Coin              Int?

    @@schema("seal_member")
}

model Idtable4 {
    id                String    @id
    passwd            String?
    vip               Int?
    viptime           Int?
    point             Int?
    reg_date          DateTime? @default(now())
    userLevel         Int?
    char_name         String?
    gameserver_burnho Int?
    serverenter_time  DateTime?
    enter_ip          String?
    record_lock       Int?
    lock_time         Int?
    web_block         DateTime?
    game_block        DateTime?
    delete_flag       Int?
    delete_date       DateTime?
    pay_flag          Int?
    update_date       DateTime?
    nick_name         String?
    lovekey           String?
    loveauth          String?
    Mac               String?
    Status            Int?
    fcsaccountguid    Int?
    recommender       String?
    LoginDate         DateTime?
    LoginCount        Int?
    email             String?
    RegIp             Int?
    Cash              Int?
    HorCah            Int?
    Coin              Int?

    @@schema("seal_member")
}

model Idtable5 {
    id                String    @id
    passwd            String?
    vip               Int?
    viptime           Int?
    point             Int?
    reg_date          DateTime? @default(now())
    userLevel         Int?
    char_name         String?
    gameserver_burnho Int?
    serverenter_time  DateTime?
    enter_ip          String?
    record_lock       Int?
    lock_time         Int?
    web_block         DateTime?
    game_block        DateTime?
    delete_flag       Int?
    delete_date       DateTime?
    pay_flag          Int?
    update_date       DateTime?
    nick_name         String?
    lovekey           String?
    loveauth          String?
    Mac               String?
    Status            Int?
    fcsaccountguid    Int?
    recommender       String?
    LoginDate         DateTime?
    LoginCount        Int?
    email             String?
    RegIp             Int?
    Cash              Int?
    HorCah            Int?
    Coin              Int?

    @@schema("seal_member")
}

model pc {
    // uid    Int    @id @default(autoincrement())
    char_name            String    @id
    user_id              String
    char_order           Int?
    map_num              Int?
    x                    Int?
    y                    Int?
    level                Int?
    sex                  Int?
    exp                  Int?
    money                Int?
    job                  Int?
    fame                 Int?
    strength             Int?
    intelligence         Int?
    dexterity            Int?
    constitution         Int?
    mentality            Int?
    sense                Int?
    hp                   Int?
    ap                   Int?
    xp                   Int?
    levelup_point        Int?
    skillup_point        Int?
    et0                  Int?
    eo0                  Int?
    es0                  Int?
    ct0                  Int?
    cs0                  Int?
    co0                  Int?
    coo0                 Int?
    et1                  Int?
    eo1                  Int?
    es1                  Int?
    ct1                  Int?
    cs1                  Int?
    co1                  Int?
    coo1                 Int?
    et2                  Int?
    eo2                  Int?
    es2                  Int?
    ct2                  Int?
    cs2                  Int?
    co2                  Int?
    coo2                 Int?
    et3                  Int?
    eo3                  Int?
    es3                  Int?
    ct3                  Int?
    cs3                  Int?
    co3                  Int?
    coo3                 Int?
    et4                  Int?
    eo4                  Int?
    es4                  Int?
    ct4                  Int?
    cs4                  Int?
    co4                  Int?
    coo4                 Int?
    et5                  Int?
    eo5                  Int?
    es5                  Int?
    ct5                  Int?
    cs5                  Int?
    co5                  Int?
    coo5                 Int?
    et6                  Int?
    eo6                  Int?
    es6                  Int?
    ct6                  Int?
    cs6                  Int?
    co6                  Int?
    coo6                 Int?
    et7                  Int?
    eo7                  Int?
    es7                  Int?
    ct7                  Int?
    cs7                  Int?
    co7                  Int?
    coo7                 Int?
    et8                  Int?
    eo8                  Int?
    es8                  Int?
    eoo8                 Int?
    et9                  Int?
    eo9                  Int?
    es9                  Int?
    eoo9                 Int?
    et10                 Int?
    eo10                 Int?
    es10                 Int?
    eoo10                Int?
    et11                 Int?
    eo11                 Int?
    es11                 Int?
    eoo11                Int?
    et12                 Int?
    eo12                 Int?
    es12                 Int?
    eoo12                Int?
    killmonsters         Int?
    total_time           Int?
    total_day            Int?
    tm_yday              Int?
    warp_time            DateTime?
    delete_date          DateTime?
    pay_flag             Int?
    can_name_change      Int?
    gw_score_t           Int?
    gw_rank_t            Int?
    gw_score_w           Int?
    gw_rank_w            Int?
    dead_map_num         Int?
    dead_x               Int?
    dead_y               Int?
    pet_timer            Int?
    expert_skillup_point Int?
    job_trans_level      Int?
    job_prev             Int?
    visual_state         Int?
    buff                 Int?
    face_style           Int?
    pvppoint             Int?
    sealpuls_flag        Int?
    isVipBag             Int?
    chaos                Int?
    fid                  Int?

    @@schema("gdb0101")
}

model inventory {
    // uid    Int    @id @default(autoincrement())
    char_name String @id
    it0       Int?
    io0       Int?
    it1       Int?
    io1       Int?
    it2       Int?
    io2       Int?
    it3       Int?
    io3       Int?
    it4       Int?
    io4       Int?
    it5       Int?
    io5       Int?
    it6       Int?
    io6       Int?
    it7       Int?
    io7       Int?

    @@schema("gdb0101")
}

model cash_inventory {
    // uid    Int    @id @default(autoincrement())
    char_name String @id
    it0       Int?
    io0       Int?
    ioo0      Int?
    it1       Int?
    io1       Int?
    ioo1      Int?
    it2       Int?
    io2       Int?
    ioo2      Int?
    it3       Int?
    io3       Int?
    ioo3      Int?
    it4       Int?
    io4       Int?
    ioo4      Int?
    it5       Int?
    io5       Int?
    ioo5      Int?
    it6       Int?
    io6       Int?
    ioo6      Int?
    it7       Int?
    io7       Int?
    ioo7      Int?

    @@schema("gdb0101")
}

model store {
    // uid    Int    @id @default(autoincrement())
    user_id String @id
    it0     Int?
    io0     Int?
    it1     Int?
    io1     Int?
    it2     Int?
    io2     Int?
    it3     Int?
    io3     Int?
    it4     Int?
    io4     Int?
    it5     Int?
    io5     Int?
    it6     Int?
    io6     Int?
    it7     Int?
    io7     Int?

    @@schema("gdb0101")
}

model quest {
    // uid    Int    @id @default(autoincrement())
    char_name String @id
    q0        Int?
    q1        Int?
    q2        Int?

    @@schema("gdb0101")
}

model seal_item {
    // uid    Int    @id @default(autoincrement())
    UniqueNum Int       @id @default(autoincrement())
    ItemType  Int?
    ItemOp1   Int?
    ItemOp2   Int?
    ItemLimit Int?
    OwnerID   String?
    OwnerDate DateTime? @default(now())
    bxaid     String?

    @@schema("item")
}

model item_default {
    // uid    Int    @id @default(autoincrement())
    code          Int     @id
    gubun         Int?
    o_name        String?
    name          String?
    ability       String?
    num           Int?
    price         Int?
    time_day      Int?
    usecnt        Int?
    job           Int?
    option_key    String?
    jeryun        Int?
    new_chk       String?
    hot_chk       String?
    event_chk     String?
    s_day         String?
    e_day         String?
    event_cnt     Int?
    event_cnt_now Int?
    memo          String?
    icon_img      String?
    icon_code     Int?
    reg_date      String?
    modify_date   String?
    reg_id        String?
    modify_id     String?
    sell_chk      String?
    item_point    Int?
    tot_cnt       Int?
    week_cnt      Int?

    @@schema("sealonline_item_mall")
}