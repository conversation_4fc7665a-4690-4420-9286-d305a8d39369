// PostgreSQL Schema for Secondary Database
// This schema is for future integration with SQL-based systems

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma-sql"
}

// PostgreSQL - Secondary Database
datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_URL")
}

// User model for PostgreSQL
model User {
  id          Int      @id @default(autoincrement())
  email       String   @unique
  username    String   @unique
  password    String
  firstName   String?
  lastName    String?
  avatar      String?
  role        UserRole @default(USER)
  isActive    Boolean  @default(true)
  isVerified  Boolean  @default(false)
  lastLogin   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  sessions    Session[]
  profile     UserProfile?
  
  @@map("users")
}

model UserProfile {
  id          Int      @id @default(autoincrement())
  userId      Int      @unique
  phone       String?
  address     String?
  dateOfBirth DateTime?
  gender      Gender?
  bio         String?
  website     String?
  socialLinks Json?
  preferences Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("user_profiles")
}

model Session {
  id          Int      @id @default(autoincrement())
  userId      Int
  token       String   @unique
  refreshToken String?
  expiresAt   DateTime
  isActive    Boolean  @default(true)
  userAgent   String?
  ipAddress   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("sessions")
}

// Enums
enum UserRole {
  ADMIN
  MODERATOR
  USER
  GUEST
}

enum Gender {
  MALE
  FEMALE
  OTHER
  PREFER_NOT_TO_SAY
}
