# WebShop Backend API Tests
# Use this file with REST Client extension in VS Code or similar tools

@baseUrl = http://localhost:3001
@contentType = application/json

### Health Check
GET {{baseUrl}}/health

### Root Endpoint
GET {{baseUrl}}/

### Register New User
POST {{baseUrl}}/api/auth/register
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "username": "testuser",
  "password": "TestPassword123!",
  "firstName": "Test",
  "lastName": "User"
}

### Login User
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "TestPassword123!",
  "rememberMe": false
}

### Get Current User Profile (requires token)
GET {{baseUrl}}/api/auth/me
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

### Change Password (requires token)
POST {{baseUrl}}/api/auth/change-password
Content-Type: {{contentType}}
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

{
  "currentPassword": "TestPassword123!",
  "newPassword": "NewPassword123!",
  "confirmPassword": "NewPassword123!"
}

### Get User Sessions (requires token)
GET {{baseUrl}}/api/auth/sessions
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

### Refresh Token
POST {{baseUrl}}/api/auth/refresh
Content-Type: {{contentType}}

{
  "refreshToken": "YOUR_REFRESH_TOKEN_HERE"
}

### Logout (requires token)
POST {{baseUrl}}/api/auth/logout
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

### Verify Token (requires token)
GET {{baseUrl}}/api/auth/verify
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

### Get Users (Admin/Moderator only)
GET {{baseUrl}}/api/users?page=1&limit=10
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

### Search Users (Admin/Moderator only)
GET {{baseUrl}}/api/users/search?q=test&limit=5
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

### Get User Stats (Admin only)
GET {{baseUrl}}/api/users/stats
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

### Get User by ID
GET {{baseUrl}}/api/users/USER_ID_HERE
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

### Update User Profile
PUT {{baseUrl}}/api/users/USER_ID_HERE/profile
Content-Type: {{contentType}}
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

{
  "firstName": "Updated",
  "lastName": "Name",
  "phone": "+1234567890",
  "bio": "Updated bio",
  "website": "https://example.com"
}

### Update User Role (Admin only)
PUT {{baseUrl}}/api/users/USER_ID_HERE/role
Content-Type: {{contentType}}
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

{
  "role": "MODERATOR"
}

### Update User Status (Admin only)
PUT {{baseUrl}}/api/users/USER_ID_HERE/status
Content-Type: {{contentType}}
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

{
  "isActive": true,
  "isVerified": true
}

### Get User Sessions
GET {{baseUrl}}/api/users/USER_ID_HERE/sessions
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

### Invalidate All User Sessions
DELETE {{baseUrl}}/api/users/USER_ID_HERE/sessions
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE

### Delete User (Admin only)
DELETE {{baseUrl}}/api/users/USER_ID_HERE
Authorization: Bearer YOUR_ACCESS_TOKEN_HERE
