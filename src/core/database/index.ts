// Database Core Module
// Handles database connections and client initialization

import { PrismaClient } from '../../generated/prisma'

// MongoDB Client (Primary Database)
export const mongoClient = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
  errorFormat: 'pretty',
})

// Database connection management
export class DatabaseManager {
  private static instance: DatabaseManager
  private isConnected = false

  private constructor() {}

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager()
    }
    return DatabaseManager.instance
  }

  async connect(): Promise<void> {
    try {
      if (!this.isConnected) {
        await mongoClient.$connect()
        this.isConnected = true
        console.log('✅ MongoDB connected successfully')
      }
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.isConnected) {
        await mongoClient.$disconnect()
        this.isConnected = false
        console.log('✅ MongoDB disconnected successfully')
      }
    } catch (error) {
      console.error('❌ MongoDB disconnection failed:', error)
      throw error
    }
  }

  getClient() {
    return mongoClient
  }

  isConnectionActive(): boolean {
    return this.isConnected
  }
}

// Export singleton instance
export const dbManager = DatabaseManager.getInstance()

// Export client for direct use
export { mongoClient as db }

// Health check function
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await mongoClient.$queryRaw`SELECT 1`
    return true
  } catch (error) {
    console.error('Database health check failed:', error)
    return false
  }
}

// Graceful shutdown handler
export async function gracefulShutdown(): Promise<void> {
  console.log('🔄 Shutting down database connections...')
  await dbManager.disconnect()
  console.log('✅ Database connections closed')
}

// Handle process termination
process.on('SIGINT', gracefulShutdown)
process.on('SIGTERM', gracefulShutdown)
process.on('beforeExit', gracefulShutdown)
