// Response Utilities
// Standardized API response helpers

export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  meta?: {
    page?: number
    limit?: number
    total?: number
    totalPages?: number
  }
  timestamp: string
}

export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export class ResponseHelper {
  /**
   * Success response
   */
  static success<T>(
    data: T,
    message: string = 'Success',
    meta?: Partial<PaginationMeta>
  ): ApiResponse<T> {
    return {
      success: true,
      message,
      data,
      meta,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Error response
   */
  static error(
    message: string,
    code: string = 'INTERNAL_ERROR',
    details?: any,
    statusCode: number = 500
  ): ApiResponse {
    return {
      success: false,
      message: 'Request failed',
      error: {
        code,
        message,
        details
      },
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Validation error response
   */
  static validationError(
    errors: any,
    message: string = 'Validation failed'
  ): ApiResponse {
    return {
      success: false,
      message,
      error: {
        code: 'VALIDATION_ERROR',
        message,
        details: errors
      },
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Not found response
   */
  static notFound(
    resource: string = 'Resource',
    message?: string
  ): ApiResponse {
    return {
      success: false,
      message: message || `${resource} not found`,
      error: {
        code: 'NOT_FOUND',
        message: message || `${resource} not found`
      },
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Unauthorized response
   */
  static unauthorized(
    message: string = 'Unauthorized access'
  ): ApiResponse {
    return {
      success: false,
      message,
      error: {
        code: 'UNAUTHORIZED',
        message
      },
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Forbidden response
   */
  static forbidden(
    message: string = 'Access forbidden'
  ): ApiResponse {
    return {
      success: false,
      message,
      error: {
        code: 'FORBIDDEN',
        message
      },
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Conflict response
   */
  static conflict(
    message: string = 'Resource conflict',
    details?: any
  ): ApiResponse {
    return {
      success: false,
      message,
      error: {
        code: 'CONFLICT',
        message,
        details
      },
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Rate limit response
   */
  static rateLimitExceeded(
    message: string = 'Rate limit exceeded'
  ): ApiResponse {
    return {
      success: false,
      message,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message
      },
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Paginated response
   */
  static paginated<T>(
    data: T[],
    page: number,
    limit: number,
    total: number,
    message: string = 'Data retrieved successfully'
  ): ApiResponse<T[]> {
    const totalPages = Math.ceil(total / limit)
    const hasNext = page < totalPages
    const hasPrev = page > 1

    return {
      success: true,
      message,
      data,
      meta: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev
      },
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Created response
   */
  static created<T>(
    data: T,
    message: string = 'Resource created successfully'
  ): ApiResponse<T> {
    return {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Updated response
   */
  static updated<T>(
    data: T,
    message: string = 'Resource updated successfully'
  ): ApiResponse<T> {
    return {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Deleted response
   */
  static deleted(
    message: string = 'Resource deleted successfully'
  ): ApiResponse {
    return {
      success: true,
      message,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * No content response
   */
  static noContent(
    message: string = 'No content'
  ): ApiResponse {
    return {
      success: true,
      message,
      timestamp: new Date().toISOString()
    }
  }
}

// HTTP Status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
} as const

// Common error codes
export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE'
} as const
