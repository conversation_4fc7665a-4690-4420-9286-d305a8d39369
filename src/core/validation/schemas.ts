// Validation Schemas
// Zod schemas for request validation

import { z } from 'zod'
import { UserRole, Gender } from '../../generated/prisma'

// User validation schemas
export const registerSchema = z.object({
  email: z
    .string()
    .email('Invalid email format')
    .min(1, 'Email is required')
    .max(255, 'Email is too long'),
  
  username: z
    .string()
    .min(3, 'Username must be at least 3 characters')
    .max(30, 'Username must not exceed 30 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens'),
  
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must not exceed 128 characters'),
  
  firstName: z
    .string()
    .min(1, 'First name is required')
    .max(50, 'First name is too long')
    .optional(),
  
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .max(50, 'Last name is too long')
    .optional()
})

export const loginSchema = z.object({
  email: z
    .string()
    .email('Invalid email format')
    .min(1, 'Email is required'),
  
  password: z
    .string()
    .min(1, 'Password is required'),
  
  rememberMe: z
    .boolean()
    .optional()
    .default(false)
})

export const updateProfileSchema = z.object({
  firstName: z
    .string()
    .min(1, 'First name cannot be empty')
    .max(50, 'First name is too long')
    .optional(),
  
  lastName: z
    .string()
    .min(1, 'Last name cannot be empty')
    .max(50, 'Last name is too long')
    .optional(),
  
  avatar: z
    .string()
    .url('Invalid avatar URL')
    .optional(),
  
  phone: z
    .string()
    .regex(/^\+?[\d\s-()]+$/, 'Invalid phone number format')
    .min(10, 'Phone number is too short')
    .max(20, 'Phone number is too long')
    .optional(),
  
  address: z
    .string()
    .max(500, 'Address is too long')
    .optional(),
  
  dateOfBirth: z
    .string()
    .datetime('Invalid date format')
    .optional()
    .transform((val) => val ? new Date(val) : undefined),
  
  gender: z
    .nativeEnum(Gender, { errorMap: () => ({ message: 'Invalid gender value' }) })
    .optional(),
  
  bio: z
    .string()
    .max(1000, 'Bio is too long')
    .optional(),
  
  website: z
    .string()
    .url('Invalid website URL')
    .optional(),
  
  socialLinks: z
    .record(z.string().url('Invalid social link URL'))
    .optional(),
  
  preferences: z
    .record(z.any())
    .optional()
})

export const changePasswordSchema = z.object({
  currentPassword: z
    .string()
    .min(1, 'Current password is required'),
  
  newPassword: z
    .string()
    .min(8, 'New password must be at least 8 characters')
    .max(128, 'New password must not exceed 128 characters'),
  
  confirmPassword: z
    .string()
    .min(1, 'Password confirmation is required')
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword']
})

export const resetPasswordRequestSchema = z.object({
  email: z
    .string()
    .email('Invalid email format')
    .min(1, 'Email is required')
})

export const resetPasswordSchema = z.object({
  token: z
    .string()
    .min(1, 'Reset token is required'),
  
  newPassword: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must not exceed 128 characters'),
  
  confirmPassword: z
    .string()
    .min(1, 'Password confirmation is required')
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword']
})

export const refreshTokenSchema = z.object({
  refreshToken: z
    .string()
    .min(1, 'Refresh token is required')
})

// Admin schemas
export const updateUserRoleSchema = z.object({
  userId: z
    .string()
    .min(1, 'User ID is required'),
  
  role: z
    .nativeEnum(UserRole, { errorMap: () => ({ message: 'Invalid role value' }) })
})

export const updateUserStatusSchema = z.object({
  userId: z
    .string()
    .min(1, 'User ID is required'),
  
  isActive: z
    .boolean(),
  
  isVerified: z
    .boolean()
    .optional()
})

// Query schemas
export const getUsersQuerySchema = z.object({
  page: z
    .string()
    .optional()
    .transform((val) => val ? parseInt(val) : 1)
    .refine((val) => val > 0, 'Page must be greater than 0'),
  
  limit: z
    .string()
    .optional()
    .transform((val) => val ? parseInt(val) : 10)
    .refine((val) => val > 0 && val <= 100, 'Limit must be between 1 and 100'),
  
  search: z
    .string()
    .optional(),
  
  role: z
    .nativeEnum(UserRole)
    .optional(),
  
  isActive: z
    .string()
    .optional()
    .transform((val) => val === 'true' ? true : val === 'false' ? false : undefined),
  
  isVerified: z
    .string()
    .optional()
    .transform((val) => val === 'true' ? true : val === 'false' ? false : undefined),
  
  sortBy: z
    .enum(['createdAt', 'updatedAt', 'email', 'username', 'lastLogin'])
    .optional()
    .default('createdAt'),
  
  sortOrder: z
    .enum(['asc', 'desc'])
    .optional()
    .default('desc')
})

// Common validation utilities
export const objectIdSchema = z
  .string()
  .regex(/^[0-9a-fA-F]{24}$/, 'Invalid ObjectId format')

export const paginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10)
})

// Type exports
export type RegisterInput = z.infer<typeof registerSchema>
export type LoginInput = z.infer<typeof loginSchema>
export type UpdateProfileInput = z.infer<typeof updateProfileSchema>
export type ChangePasswordInput = z.infer<typeof changePasswordSchema>
export type ResetPasswordRequestInput = z.infer<typeof resetPasswordRequestSchema>
export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>
export type RefreshTokenInput = z.infer<typeof refreshTokenSchema>
export type UpdateUserRoleInput = z.infer<typeof updateUserRoleSchema>
export type UpdateUserStatusInput = z.infer<typeof updateUserStatusSchema>
export type GetUsersQueryInput = z.infer<typeof getUsersQuerySchema>
