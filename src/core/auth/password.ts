// Password Utilities
// Handles password hashing, verification, and validation

import bcrypt from 'bcryptjs'
import { AuthError, AuthErrorType } from './types'

export class PasswordManager {
  private readonly saltRounds: number

  constructor() {
    this.saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS || '12')
  }

  /**
   * Hash password using bcrypt
   */
  async hashPassword(password: string): Promise<string> {
    try {
      // Validate password strength before hashing
      this.validatePasswordStrength(password)
      
      const salt = await bcrypt.genSalt(this.saltRounds)
      return await bcrypt.hash(password, salt)
    } catch (error) {
      if (error instanceof AuthError) {
        throw error
      }
      throw new AuthError(
        AuthErrorType.WEAK_PASSWORD,
        'Failed to hash password',
        500
      )
    }
  }

  /**
   * Verify password against hash
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, hash)
    } catch (error) {
      console.error('Password verification error:', error)
      return false
    }
  }

  /**
   * Validate password strength
   */
  validatePasswordStrength(password: string): void {
    const minLength = 8
    const maxLength = 128

    // Check length
    if (password.length < minLength) {
      throw new AuthError(
        AuthErrorType.WEAK_PASSWORD,
        `Password must be at least ${minLength} characters long`,
        400
      )
    }

    if (password.length > maxLength) {
      throw new AuthError(
        AuthErrorType.WEAK_PASSWORD,
        `Password must not exceed ${maxLength} characters`,
        400
      )
    }

    // Check for at least one lowercase letter
    if (!/[a-z]/.test(password)) {
      throw new AuthError(
        AuthErrorType.WEAK_PASSWORD,
        'Password must contain at least one lowercase letter',
        400
      )
    }

    // Check for at least one uppercase letter
    if (!/[A-Z]/.test(password)) {
      throw new AuthError(
        AuthErrorType.WEAK_PASSWORD,
        'Password must contain at least one uppercase letter',
        400
      )
    }

    // Check for at least one number
    if (!/\d/.test(password)) {
      throw new AuthError(
        AuthErrorType.WEAK_PASSWORD,
        'Password must contain at least one number',
        400
      )
    }

    // Check for at least one special character
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      throw new AuthError(
        AuthErrorType.WEAK_PASSWORD,
        'Password must contain at least one special character',
        400
      )
    }

    // Check for common weak passwords
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ]

    if (commonPasswords.includes(password.toLowerCase())) {
      throw new AuthError(
        AuthErrorType.WEAK_PASSWORD,
        'Password is too common. Please choose a stronger password',
        400
      )
    }

    // Check for sequential characters
    if (this.hasSequentialChars(password)) {
      throw new AuthError(
        AuthErrorType.WEAK_PASSWORD,
        'Password should not contain sequential characters',
        400
      )
    }

    // Check for repeated characters
    if (this.hasRepeatedChars(password)) {
      throw new AuthError(
        AuthErrorType.WEAK_PASSWORD,
        'Password should not contain too many repeated characters',
        400
      )
    }
  }

  /**
   * Generate a random password
   */
  generateRandomPassword(length: number = 16): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz'
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const numbers = '0123456789'
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?'
    
    const allChars = lowercase + uppercase + numbers + symbols
    let password = ''

    // Ensure at least one character from each category
    password += lowercase[Math.floor(Math.random() * lowercase.length)]
    password += uppercase[Math.floor(Math.random() * uppercase.length)]
    password += numbers[Math.floor(Math.random() * numbers.length)]
    password += symbols[Math.floor(Math.random() * symbols.length)]

    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)]
    }

    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('')
  }

  /**
   * Check for sequential characters (e.g., "abc", "123")
   */
  private hasSequentialChars(password: string): boolean {
    for (let i = 0; i < password.length - 2; i++) {
      const char1 = password.charCodeAt(i)
      const char2 = password.charCodeAt(i + 1)
      const char3 = password.charCodeAt(i + 2)

      if (char2 === char1 + 1 && char3 === char2 + 1) {
        return true
      }
    }
    return false
  }

  /**
   * Check for repeated characters (more than 3 in a row)
   */
  private hasRepeatedChars(password: string): boolean {
    let count = 1
    for (let i = 1; i < password.length; i++) {
      if (password[i] === password[i - 1]) {
        count++
        if (count > 3) {
          return true
        }
      } else {
        count = 1
      }
    }
    return false
  }
}

// Export singleton instance
export const passwordManager = new PasswordManager()
