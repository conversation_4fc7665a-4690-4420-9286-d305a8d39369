// Authentication Types
// Defines types and interfaces for authentication system

import { UserRole } from '../../generated/prisma'

export interface JWTPayload {
  userId: string
  email: string
  username: string
  role: UserRole
  sessionId: string
  iat?: number
  exp?: number
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
  tokenType: 'Bearer'
}

export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  email: string
  username: string
  password: string
  firstName?: string
  lastName?: string
}

export interface AuthUser {
  id: string
  email: string
  username: string
  firstName?: string
  lastName?: string
  avatar?: string
  role: UserRole
  isActive: boolean
  isVerified: boolean
  lastLogin?: Date
  createdAt: Date
  updatedAt: Date
}

export interface AuthSession {
  id: string
  userId: string
  token: string
  refreshToken?: string
  expiresAt: Date
  isActive: boolean
  userAgent?: string
  ipAddress?: string
  createdAt: Date
  updatedAt: Date
}

export interface AuthContext {
  user: AuthUser | null
  session: AuthSession | null
  isAuthenticated: boolean
}

export interface PasswordResetRequest {
  email: string
  token: string
  expiresAt: Date
}

export interface ChangePasswordData {
  currentPassword: string
  newPassword: string
}

export interface ResetPasswordData {
  token: string
  newPassword: string
}

// Auth middleware context
export interface AuthMiddlewareContext {
  user?: AuthUser
  session?: AuthSession
  token?: string
}

// Permission types
export type Permission = 
  | 'user:read'
  | 'user:write'
  | 'user:delete'
  | 'admin:read'
  | 'admin:write'
  | 'admin:delete'
  | 'moderator:read'
  | 'moderator:write'

export interface RolePermissions {
  [UserRole.ADMIN]: Permission[]
  [UserRole.MODERATOR]: Permission[]
  [UserRole.USER]: Permission[]
  [UserRole.GUEST]: Permission[]
}

// Auth error types
export enum AuthErrorType {
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  USER_INACTIVE = 'USER_INACTIVE',
  USER_NOT_VERIFIED = 'USER_NOT_VERIFIED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  SESSION_INVALID = 'SESSION_INVALID',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  EMAIL_ALREADY_EXISTS = 'EMAIL_ALREADY_EXISTS',
  USERNAME_ALREADY_EXISTS = 'USERNAME_ALREADY_EXISTS',
  WEAK_PASSWORD = 'WEAK_PASSWORD',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED'
}

export class AuthError extends Error {
  constructor(
    public type: AuthErrorType,
    message: string,
    public statusCode: number = 401
  ) {
    super(message)
    this.name = 'AuthError'
  }
}
