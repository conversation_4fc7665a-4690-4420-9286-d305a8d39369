// JWT Utilities
// Handles JWT token creation, verification, and management

import jwt from 'jsonwebtoken'
import { JWTPayload, AuthTokens, AuthError, AuthErrorType } from './types'

export class JWTManager {
  private readonly secret: string
  private readonly expiresIn: string
  private readonly refreshExpiresIn: string

  constructor() {
    this.secret = process.env.JWT_SECRET || 'fallback-secret-key'
    this.expiresIn = process.env.JWT_EXPIRES_IN || '7d'
    this.refreshExpiresIn = process.env.JWT_REFRESH_EXPIRES_IN || '30d'

    if (this.secret === 'fallback-secret-key') {
      console.warn('⚠️  Using fallback JWT secret. Please set JWT_SECRET in environment variables.')
    }
  }

  /**
   * Generate access and refresh tokens
   */
  generateTokens(payload: Omit<JWTPayload, 'iat' | 'exp'>): AuthTokens {
    const accessToken = jwt.sign(payload, this.secret, {
      expiresIn: this.expiresIn,
      issuer: 'webshop-backend',
      audience: 'webshop-frontend'
    })

    const refreshToken = jwt.sign(
      { userId: payload.userId, sessionId: payload.sessionId },
      this.secret,
      {
        expiresIn: this.refreshExpiresIn,
        issuer: 'webshop-backend',
        audience: 'webshop-frontend'
      }
    )

    // Calculate expiration time in seconds
    const expiresIn = this.parseExpirationTime(this.expiresIn)

    return {
      accessToken,
      refreshToken,
      expiresIn,
      tokenType: 'Bearer'
    }
  }

  /**
   * Verify and decode JWT token
   */
  verifyToken(token: string): JWTPayload {
    try {
      const decoded = jwt.verify(token, this.secret, {
        issuer: 'webshop-backend',
        audience: 'webshop-frontend'
      }) as JWTPayload

      return decoded
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new AuthError(AuthErrorType.TOKEN_EXPIRED, 'Token has expired', 401)
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new AuthError(AuthErrorType.TOKEN_INVALID, 'Invalid token', 401)
      } else {
        throw new AuthError(AuthErrorType.TOKEN_INVALID, 'Token verification failed', 401)
      }
    }
  }

  /**
   * Verify refresh token
   */
  verifyRefreshToken(token: string): { userId: string; sessionId: string } {
    try {
      const decoded = jwt.verify(token, this.secret, {
        issuer: 'webshop-backend',
        audience: 'webshop-frontend'
      }) as { userId: string; sessionId: string }

      return decoded
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new AuthError(AuthErrorType.TOKEN_EXPIRED, 'Refresh token has expired', 401)
      } else {
        throw new AuthError(AuthErrorType.TOKEN_INVALID, 'Invalid refresh token', 401)
      }
    }
  }

  /**
   * Extract token from Authorization header
   */
  extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader) return null

    const parts = authHeader.split(' ')
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null
    }

    return parts[1]
  }

  /**
   * Check if token is expired without throwing error
   */
  isTokenExpired(token: string): boolean {
    try {
      jwt.verify(token, this.secret)
      return false
    } catch (error) {
      return error instanceof jwt.TokenExpiredError
    }
  }

  /**
   * Decode token without verification (for debugging)
   */
  decodeToken(token: string): JWTPayload | null {
    try {
      return jwt.decode(token) as JWTPayload
    } catch {
      return null
    }
  }

  /**
   * Parse expiration time string to seconds
   */
  private parseExpirationTime(expiresIn: string): number {
    const match = expiresIn.match(/^(\d+)([smhd])$/)
    if (!match) return 3600 // Default 1 hour

    const value = parseInt(match[1])
    const unit = match[2]

    switch (unit) {
      case 's': return value
      case 'm': return value * 60
      case 'h': return value * 3600
      case 'd': return value * 86400
      default: return 3600
    }
  }
}

// Export singleton instance
export const jwtManager = new JWTManager()
