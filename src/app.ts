import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'
import { config } from 'dotenv'

// Load environment variables
config()

// Import database manager
import { dbManager } from './core/database'

// Import routes
import { auth } from './modules/auth/routes'
import { users } from './modules/user/routes'

// Import utilities
import { ResponseHelper, HTTP_STATUS } from './core/utils/response'

// Create Hono app
const app = new Hono()

// Global middleware
app.use('*', logger())
app.use('*', prettyJSON())
app.use('*', cors({
  origin: ['http://localhost:3000', 'http://localhost:5173'], // Add your frontend URLs
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}))

// Health check endpoint
app.get('/health', async (c) => {
  try {
    const isDbConnected = dbManager.isConnectionActive()
    
    return c.json(
      ResponseHelper.success({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        database: isDbConnected ? 'connected' : 'disconnected',
        version: '1.0.0'
      }, 'Service is healthy'),
      HTTP_STATUS.OK
    )
  } catch (error) {
    return c.json(
      ResponseHelper.error('Health check failed', 'HEALTH_CHECK_ERROR'),
      HTTP_STATUS.SERVICE_UNAVAILABLE
    )
  }
})

// API routes
app.route('/api/auth', auth)
app.route('/api/users', users)

// Root endpoint
app.get('/', (c) => {
  return c.json(
    ResponseHelper.success({
      message: 'WebShop Backend API',
      version: '1.0.0',
      endpoints: {
        health: '/health',
        auth: '/api/auth',
        users: '/api/users'
      }
    }, 'Welcome to WebShop Backend API'),
    HTTP_STATUS.OK
  )
})

// 404 handler
app.notFound((c) => {
  return c.json(
    ResponseHelper.notFound('Endpoint'),
    HTTP_STATUS.NOT_FOUND
  )
})

// Error handler
app.onError((err, c) => {
  console.error('Unhandled error:', err)
  
  return c.json(
    ResponseHelper.error('Internal server error', 'INTERNAL_ERROR'),
    HTTP_STATUS.INTERNAL_SERVER_ERROR
  )
})

// Initialize database connection
async function initializeApp() {
  try {
    console.log('🔄 Initializing application...')
    
    // Connect to database
    await dbManager.connect()
    
    const port = parseInt(process.env.PORT || '3001')
    
    console.log(`🚀 Server starting on port ${port}...`)
    
    // Start server
    const server = {
      fetch: app.fetch,
      port
    }
    
    console.log(`✅ HTTP Server running on http://localhost:${port}`)
    console.log(`📚 API Documentation available at http://localhost:${port}`)
    console.log(`🏥 Health check available at http://localhost:${port}/health`)
    
    return server
  } catch (error) {
    console.error('❌ Failed to initialize application:', error)
    process.exit(1)
  }
}

// Start the application
const server = await initializeApp()

export default server
