// Authentication Routes
// API endpoints for authentication operations

import { <PERSON><PERSON> } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { AuthService } from './service'
import { authMiddleware, getAuthUser, getAuthToken } from './middleware'
import {
  registerSchema,
  loginSchema,
  changePasswordSchema,
  refreshTokenSchema
} from '../../core/validation/schemas'
import { ResponseHelper, HTTP_STATUS } from '../../core/utils/response'
import { AuthError } from '../../core/auth/types'

const auth = new Hono()
const authService = new AuthService()

/**
 * POST /auth/register
 * Register a new user
 */
auth.post(
  '/register',
  authMiddleware.rateLimit(5, 15 * 60 * 1000), // 5 requests per 15 minutes
  zValidator('json', registerSchema),
  async (c) => {
    try {
      const data = c.req.valid('json')
      const userAgent = c.req.header('user-agent')
      const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip')

      const result = await authService.register(data)

      return c.json(
        ResponseHelper.created({
          user: {
            id: result.user.id,
            email: result.user.email,
            username: result.user.username,
            firstName: result.user.firstName,
            lastName: result.user.lastName,
            avatar: result.user.avatar,
            role: result.user.role,
            isActive: result.user.isActive,
            isVerified: result.user.isVerified,
            createdAt: result.user.createdAt
          },
          tokens: result.tokens
        }, 'User registered successfully'),
        HTTP_STATUS.CREATED
      )
    } catch (error) {
      if (error instanceof AuthError) {
        return c.json(
          ResponseHelper.error(error.message, error.type, null, error.statusCode),
          error.statusCode
        )
      }

      console.error('Register error:', error)
      return c.json(
        ResponseHelper.error('Registration failed', 'REGISTRATION_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * POST /auth/login
 * Login user
 */
auth.post(
  '/login',
  authMiddleware.rateLimit(10, 15 * 60 * 1000), // 10 requests per 15 minutes
  zValidator('json', loginSchema),
  async (c) => {
    try {
      const credentials = c.req.valid('json')
      const userAgent = c.req.header('user-agent')
      const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip')

      const result = await authService.login(credentials, userAgent, ipAddress)

      return c.json(
        ResponseHelper.success({
          user: {
            id: result.user.id,
            email: result.user.email,
            username: result.user.username,
            firstName: result.user.firstName,
            lastName: result.user.lastName,
            avatar: result.user.avatar,
            role: result.user.role,
            isActive: result.user.isActive,
            isVerified: result.user.isVerified,
            lastLogin: result.user.lastLogin
          },
          tokens: result.tokens
        }, 'Login successful'),
        HTTP_STATUS.OK
      )
    } catch (error) {
      if (error instanceof AuthError) {
        return c.json(
          ResponseHelper.error(error.message, error.type, null, error.statusCode),
          error.statusCode
        )
      }

      console.error('Login error:', error)
      return c.json(
        ResponseHelper.error('Login failed', 'LOGIN_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * POST /auth/logout
 * Logout user
 */
auth.post(
  '/logout',
  authMiddleware.authenticate(),
  async (c) => {
    try {
      const token = getAuthToken(c)
      
      if (token) {
        await authService.logout(token)
      }

      return c.json(
        ResponseHelper.success(null, 'Logout successful'),
        HTTP_STATUS.OK
      )
    } catch (error) {
      console.error('Logout error:', error)
      return c.json(
        ResponseHelper.success(null, 'Logout completed'),
        HTTP_STATUS.OK
      )
    }
  }
)

/**
 * POST /auth/refresh
 * Refresh access token
 */
auth.post(
  '/refresh',
  authMiddleware.rateLimit(20, 15 * 60 * 1000), // 20 requests per 15 minutes
  zValidator('json', refreshTokenSchema),
  async (c) => {
    try {
      const { refreshToken } = c.req.valid('json')
      
      const tokens = await authService.refreshToken(refreshToken)

      return c.json(
        ResponseHelper.success(tokens, 'Token refreshed successfully'),
        HTTP_STATUS.OK
      )
    } catch (error) {
      if (error instanceof AuthError) {
        return c.json(
          ResponseHelper.error(error.message, error.type, null, error.statusCode),
          error.statusCode
        )
      }

      console.error('Refresh token error:', error)
      return c.json(
        ResponseHelper.error('Token refresh failed', 'REFRESH_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * GET /auth/me
 * Get current user profile
 */
auth.get(
  '/me',
  authMiddleware.authenticate(),
  async (c) => {
    try {
      const user = getAuthUser(c)

      return c.json(
        ResponseHelper.success({
          id: user.id,
          email: user.email,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          avatar: user.avatar,
          role: user.role,
          isActive: user.isActive,
          isVerified: user.isVerified,
          lastLogin: user.lastLogin,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }, 'User profile retrieved successfully'),
        HTTP_STATUS.OK
      )
    } catch (error) {
      console.error('Get profile error:', error)
      return c.json(
        ResponseHelper.error('Failed to get user profile', 'PROFILE_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * POST /auth/change-password
 * Change user password
 */
auth.post(
  '/change-password',
  authMiddleware.authenticate(),
  authMiddleware.rateLimit(5, 60 * 60 * 1000), // 5 requests per hour
  zValidator('json', changePasswordSchema),
  async (c) => {
    try {
      const user = getAuthUser(c)
      const data = c.req.valid('json')

      await authService.changePassword(user.id, data)

      return c.json(
        ResponseHelper.success(null, 'Password changed successfully'),
        HTTP_STATUS.OK
      )
    } catch (error) {
      if (error instanceof AuthError) {
        return c.json(
          ResponseHelper.error(error.message, error.type, null, error.statusCode),
          error.statusCode
        )
      }

      console.error('Change password error:', error)
      return c.json(
        ResponseHelper.error('Failed to change password', 'PASSWORD_CHANGE_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * GET /auth/sessions
 * Get user sessions
 */
auth.get(
  '/sessions',
  authMiddleware.authenticate(),
  async (c) => {
    try {
      const user = getAuthUser(c)
      const sessions = await authService.getUserSessions(user.id)

      return c.json(
        ResponseHelper.success(
          sessions.map(session => ({
            id: session.id,
            expiresAt: session.expiresAt,
            isActive: session.isActive,
            userAgent: session.userAgent,
            ipAddress: session.ipAddress,
            createdAt: session.createdAt,
            updatedAt: session.updatedAt
          })),
          'Sessions retrieved successfully'
        ),
        HTTP_STATUS.OK
      )
    } catch (error) {
      console.error('Get sessions error:', error)
      return c.json(
        ResponseHelper.error('Failed to get sessions', 'SESSIONS_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * DELETE /auth/sessions
 * Invalidate all user sessions
 */
auth.delete(
  '/sessions',
  authMiddleware.authenticate(),
  async (c) => {
    try {
      const user = getAuthUser(c)
      await authService.invalidateAllSessions(user.id)

      return c.json(
        ResponseHelper.success(null, 'All sessions invalidated successfully'),
        HTTP_STATUS.OK
      )
    } catch (error) {
      console.error('Invalidate sessions error:', error)
      return c.json(
        ResponseHelper.error('Failed to invalidate sessions', 'SESSIONS_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * GET /auth/verify
 * Verify token (for debugging/testing)
 */
auth.get(
  '/verify',
  authMiddleware.authenticate(),
  async (c) => {
    try {
      const user = getAuthUser(c)

      return c.json(
        ResponseHelper.success({
          valid: true,
          user: {
            id: user.id,
            email: user.email,
            username: user.username,
            role: user.role,
            isActive: user.isActive,
            isVerified: user.isVerified
          }
        }, 'Token is valid'),
        HTTP_STATUS.OK
      )
    } catch (error) {
      return c.json(
        ResponseHelper.unauthorized('Invalid token'),
        HTTP_STATUS.UNAUTHORIZED
      )
    }
  }
)

export { auth }
