// Authentication Service
// Business logic for authentication operations

import { UserRepository } from '../user/repository'
import { jwtManager } from '../../core/auth/jwt'
import { passwordManager } from '../../core/auth/password'
import {
  AuthTokens,
  LoginCredentials,
  RegisterData,
  AuthUser,
  AuthSession,
  AuthError,
  AuthErrorType,
  ChangePasswordData,
  ResetPasswordData
} from '../../core/auth/types'
import { UserDto, SessionDto, UserError, UserErrorType } from '../user/types'

export class AuthService {
  private userRepository: UserRepository

  constructor() {
    this.userRepository = new UserRepository()
  }

  /**
   * Register a new user
   */
  async register(data: RegisterData): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    try {
      // Hash password
      const hashedPassword = await passwordManager.hashPassword(data.password)

      // Create user
      const user = await this.userRepository.create({
        ...data,
        password: hashedPassword
      })

      // Convert to AuthUser
      const authUser: AuthUser = {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        isVerified: user.isVerified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }

      // Generate tokens
      const tokens = jwtManager.generateTokens({
        userId: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
        sessionId: '' // Will be updated after session creation
      })

      // Create session
      const session = await this.userRepository.createSession({
        userId: user.id,
        token: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresAt: new Date(Date.now() + (tokens.expiresIn * 1000))
      })

      // Update tokens with session ID
      const finalTokens = jwtManager.generateTokens({
        userId: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
        sessionId: session.id
      })

      // Update session with new token
      await this.userRepository.updateSession(session.id, {
        token: finalTokens.accessToken
      })

      return { user: authUser, tokens: finalTokens }
    } catch (error) {
      if (error instanceof UserError) {
        throw new AuthError(
          error.type as any,
          error.message,
          error.statusCode
        )
      }
      throw error
    }
  }

  /**
   * Login user
   */
  async login(credentials: LoginCredentials, userAgent?: string, ipAddress?: string): Promise<{ user: AuthUser; tokens: AuthTokens }> {
    try {
      // Find user by email
      const user = await this.userRepository.findByEmail(credentials.email)
      if (!user) {
        throw new AuthError(
          AuthErrorType.INVALID_CREDENTIALS,
          'Invalid email or password',
          401
        )
      }

      // Check if user is active
      if (!user.isActive) {
        throw new AuthError(
          AuthErrorType.USER_INACTIVE,
          'User account is inactive',
          401
        )
      }

      // Verify password
      const isPasswordValid = await passwordManager.verifyPassword(
        credentials.password,
        user.password
      )

      if (!isPasswordValid) {
        throw new AuthError(
          AuthErrorType.INVALID_CREDENTIALS,
          'Invalid email or password',
          401
        )
      }

      // Update last login
      await this.userRepository.updateLastLogin(user.id)

      // Convert to AuthUser
      const authUser: AuthUser = {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        isVerified: user.isVerified,
        lastLogin: new Date(),
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }

      // Generate tokens
      const tokens = jwtManager.generateTokens({
        userId: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
        sessionId: '' // Will be updated after session creation
      })

      // Create session
      const session = await this.userRepository.createSession({
        userId: user.id,
        token: tokens.accessToken,
        refreshToken: tokens.refreshToken,
        expiresAt: new Date(Date.now() + (tokens.expiresIn * 1000)),
        userAgent,
        ipAddress
      })

      // Update tokens with session ID
      const finalTokens = jwtManager.generateTokens({
        userId: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
        sessionId: session.id
      })

      // Update session with new token
      await this.userRepository.updateSession(session.id, {
        token: finalTokens.accessToken
      })

      return { user: authUser, tokens: finalTokens }
    } catch (error) {
      if (error instanceof AuthError) {
        throw error
      }
      console.error('Login error:', error)
      throw new AuthError(
        AuthErrorType.INVALID_CREDENTIALS,
        'Login failed',
        401
      )
    }
  }

  /**
   * Logout user
   */
  async logout(token: string): Promise<void> {
    try {
      const session = await this.userRepository.findSessionByToken(token)
      if (session) {
        await this.userRepository.deleteSession(session.id)
      }
    } catch (error) {
      console.error('Logout error:', error)
      // Don't throw error for logout
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<AuthTokens> {
    try {
      // Verify refresh token
      const decoded = jwtManager.verifyRefreshToken(refreshToken)

      // Find session
      const session = await this.userRepository.findSessionByToken(refreshToken)
      if (!session || !session.isActive || session.expiresAt < new Date()) {
        throw new AuthError(
          AuthErrorType.SESSION_EXPIRED,
          'Session expired',
          401
        )
      }

      // Find user
      const user = await this.userRepository.findById(decoded.userId)
      if (!user || !user.isActive) {
        throw new AuthError(
          AuthErrorType.USER_NOT_FOUND,
          'User not found or inactive',
          401
        )
      }

      // Generate new tokens
      const tokens = jwtManager.generateTokens({
        userId: user.id,
        email: user.email,
        username: user.username,
        role: user.role,
        sessionId: session.id
      })

      // Update session
      await this.userRepository.updateSession(session.id, {
        token: tokens.accessToken,
        expiresAt: new Date(Date.now() + (tokens.expiresIn * 1000))
      })

      return tokens
    } catch (error) {
      if (error instanceof AuthError) {
        throw error
      }
      throw new AuthError(
        AuthErrorType.TOKEN_INVALID,
        'Invalid refresh token',
        401
      )
    }
  }

  /**
   * Verify access token and get user
   */
  async verifyToken(token: string): Promise<{ user: AuthUser; session: AuthSession }> {
    try {
      // Verify JWT token
      const decoded = jwtManager.verifyToken(token)

      // Find session
      const session = await this.userRepository.findSessionByToken(token)
      if (!session || !session.isActive || session.expiresAt < new Date()) {
        throw new AuthError(
          AuthErrorType.SESSION_EXPIRED,
          'Session expired',
          401
        )
      }

      // Find user
      const user = await this.userRepository.findById(decoded.userId)
      if (!user || !user.isActive) {
        throw new AuthError(
          AuthErrorType.USER_NOT_FOUND,
          'User not found or inactive',
          401
        )
      }

      const authUser: AuthUser = {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        isVerified: user.isVerified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }

      const authSession: AuthSession = {
        id: session.id,
        userId: session.userId,
        token: session.token,
        refreshToken: session.refreshToken,
        expiresAt: session.expiresAt,
        isActive: session.isActive,
        userAgent: session.userAgent,
        ipAddress: session.ipAddress,
        createdAt: session.createdAt,
        updatedAt: session.updatedAt
      }

      return { user: authUser, session: authSession }
    } catch (error) {
      if (error instanceof AuthError) {
        throw error
      }
      throw new AuthError(
        AuthErrorType.TOKEN_INVALID,
        'Invalid token',
        401
      )
    }
  }

  /**
   * Change user password
   */
  async changePassword(userId: string, data: ChangePasswordData): Promise<void> {
    try {
      // Find user
      const user = await this.userRepository.findById(userId)
      if (!user) {
        throw new AuthError(
          AuthErrorType.USER_NOT_FOUND,
          'User not found',
          404
        )
      }

      // Verify current password
      const isCurrentPasswordValid = await passwordManager.verifyPassword(
        data.currentPassword,
        user.password
      )

      if (!isCurrentPasswordValid) {
        throw new AuthError(
          AuthErrorType.INVALID_CREDENTIALS,
          'Current password is incorrect',
          400
        )
      }

      // Hash new password
      const hashedNewPassword = await passwordManager.hashPassword(data.newPassword)

      // Update password
      await this.userRepository.update(userId, {
        password: hashedNewPassword
      })

      // Invalidate all user sessions except current one
      await this.userRepository.deleteUserSessions(userId)
    } catch (error) {
      if (error instanceof AuthError) {
        throw error
      }
      throw new AuthError(
        AuthErrorType.INVALID_CREDENTIALS,
        'Failed to change password',
        400
      )
    }
  }

  /**
   * Get user sessions
   */
  async getUserSessions(userId: string): Promise<SessionDto[]> {
    try {
      const sessions = await this.userRepository.findUserSessions(userId)
      return sessions.map(session => ({
        id: session.id,
        userId: session.userId,
        token: session.token,
        expiresAt: session.expiresAt,
        isActive: session.isActive,
        userAgent: session.userAgent,
        ipAddress: session.ipAddress,
        createdAt: session.createdAt,
        updatedAt: session.updatedAt
      }))
    } catch (error) {
      console.error('Error getting user sessions:', error)
      return []
    }
  }

  /**
   * Invalidate all user sessions
   */
  async invalidateAllSessions(userId: string): Promise<void> {
    try {
      await this.userRepository.deleteUserSessions(userId)
    } catch (error) {
      console.error('Error invalidating all sessions:', error)
      throw new AuthError(
        AuthErrorType.SESSION_INVALID,
        'Failed to invalidate sessions',
        500
      )
    }
  }
}
