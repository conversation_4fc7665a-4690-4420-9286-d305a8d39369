// Authentication Middleware
// Middleware for protecting routes and handling authentication

import { Context, Next } from 'hono'
import { jwtManager } from '../../core/auth/jwt'
import { AuthService } from './service'
import { UserRole } from '../../generated/prisma'
import { AuthError, AuthErrorType, AuthMiddlewareContext } from '../../core/auth/types'
import { ResponseHelper, HTTP_STATUS } from '../../core/utils/response'

export class AuthMiddleware {
  private authService: AuthService

  constructor() {
    this.authService = new AuthService()
  }

  /**
   * Middleware to authenticate user
   */
  authenticate() {
    return async (c: Context, next: Next) => {
      try {
        const authHeader = c.req.header('Authorization')
        const token = jwtManager.extractTokenFromHeader(authHeader)

        if (!token) {
          return c.json(
            ResponseHelper.unauthorized('Access token required'),
            HTTP_STATUS.UNAUTHORIZED
          )
        }

        // Verify token and get user
        const { user, session } = await this.authService.verifyToken(token)

        // Add user and session to context
        c.set('user', user)
        c.set('session', session)
        c.set('token', token)

        await next()
      } catch (error) {
        if (error instanceof AuthError) {
          return c.json(
            ResponseHelper.unauthorized(error.message),
            HTTP_STATUS.UNAUTHORIZED
          )
        }

        console.error('Authentication middleware error:', error)
        return c.json(
          ResponseHelper.error('Authentication failed', 'AUTH_ERROR'),
          HTTP_STATUS.UNAUTHORIZED
        )
      }
    }
  }

  /**
   * Middleware to check if user has required role
   */
  requireRole(requiredRoles: UserRole | UserRole[]) {
    return async (c: Context, next: Next) => {
      try {
        const user = c.get('user')
        
        if (!user) {
          return c.json(
            ResponseHelper.unauthorized('Authentication required'),
            HTTP_STATUS.UNAUTHORIZED
          )
        }

        const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles]
        
        if (!roles.includes(user.role)) {
          return c.json(
            ResponseHelper.forbidden('Insufficient permissions'),
            HTTP_STATUS.FORBIDDEN
          )
        }

        await next()
      } catch (error) {
        console.error('Role middleware error:', error)
        return c.json(
          ResponseHelper.forbidden('Access denied'),
          HTTP_STATUS.FORBIDDEN
        )
      }
    }
  }

  /**
   * Middleware to check if user is admin
   */
  requireAdmin() {
    return this.requireRole(UserRole.ADMIN)
  }

  /**
   * Middleware to check if user is admin or moderator
   */
  requireModerator() {
    return this.requireRole([UserRole.ADMIN, UserRole.MODERATOR])
  }

  /**
   * Middleware to check if user is verified
   */
  requireVerified() {
    return async (c: Context, next: Next) => {
      try {
        const user = c.get('user')
        
        if (!user) {
          return c.json(
            ResponseHelper.unauthorized('Authentication required'),
            HTTP_STATUS.UNAUTHORIZED
          )
        }

        if (!user.isVerified) {
          return c.json(
            ResponseHelper.forbidden('Account verification required'),
            HTTP_STATUS.FORBIDDEN
          )
        }

        await next()
      } catch (error) {
        console.error('Verification middleware error:', error)
        return c.json(
          ResponseHelper.forbidden('Verification required'),
          HTTP_STATUS.FORBIDDEN
        )
      }
    }
  }

  /**
   * Middleware to check if user is active
   */
  requireActive() {
    return async (c: Context, next: Next) => {
      try {
        const user = c.get('user')
        
        if (!user) {
          return c.json(
            ResponseHelper.unauthorized('Authentication required'),
            HTTP_STATUS.UNAUTHORIZED
          )
        }

        if (!user.isActive) {
          return c.json(
            ResponseHelper.forbidden('Account is inactive'),
            HTTP_STATUS.FORBIDDEN
          )
        }

        await next()
      } catch (error) {
        console.error('Active middleware error:', error)
        return c.json(
          ResponseHelper.forbidden('Account inactive'),
          HTTP_STATUS.FORBIDDEN
        )
      }
    }
  }

  /**
   * Optional authentication middleware
   * Sets user context if token is valid, but doesn't require authentication
   */
  optionalAuth() {
    return async (c: Context, next: Next) => {
      try {
        const authHeader = c.req.header('Authorization')
        const token = jwtManager.extractTokenFromHeader(authHeader)

        if (token) {
          try {
            const { user, session } = await this.authService.verifyToken(token)
            c.set('user', user)
            c.set('session', session)
            c.set('token', token)
          } catch (error) {
            // Ignore authentication errors for optional auth
            console.log('Optional auth failed:', error.message)
          }
        }

        await next()
      } catch (error) {
        console.error('Optional auth middleware error:', error)
        await next()
      }
    }
  }

  /**
   * Middleware to check if user owns the resource or is admin
   */
  requireOwnershipOrAdmin(userIdParam: string = 'userId') {
    return async (c: Context, next: Next) => {
      try {
        const user = c.get('user')
        
        if (!user) {
          return c.json(
            ResponseHelper.unauthorized('Authentication required'),
            HTTP_STATUS.UNAUTHORIZED
          )
        }

        const resourceUserId = c.req.param(userIdParam)
        
        // Allow if user is admin or owns the resource
        if (user.role === UserRole.ADMIN || user.id === resourceUserId) {
          await next()
          return
        }

        return c.json(
          ResponseHelper.forbidden('Access denied'),
          HTTP_STATUS.FORBIDDEN
        )
      } catch (error) {
        console.error('Ownership middleware error:', error)
        return c.json(
          ResponseHelper.forbidden('Access denied'),
          HTTP_STATUS.FORBIDDEN
        )
      }
    }
  }

  /**
   * Rate limiting middleware (basic implementation)
   */
  rateLimit(maxRequests: number = 100, windowMs: number = 15 * 60 * 1000) {
    const requests = new Map<string, { count: number; resetTime: number }>()

    return async (c: Context, next: Next) => {
      try {
        const clientId = c.req.header('x-forwarded-for') || 
                        c.req.header('x-real-ip') || 
                        'unknown'

        const now = Date.now()
        const windowStart = now - windowMs

        // Clean up old entries
        for (const [key, value] of requests.entries()) {
          if (value.resetTime < windowStart) {
            requests.delete(key)
          }
        }

        // Get or create client record
        let clientRecord = requests.get(clientId)
        if (!clientRecord || clientRecord.resetTime < windowStart) {
          clientRecord = { count: 0, resetTime: now + windowMs }
          requests.set(clientId, clientRecord)
        }

        // Check rate limit
        if (clientRecord.count >= maxRequests) {
          return c.json(
            ResponseHelper.rateLimitExceeded('Too many requests'),
            HTTP_STATUS.TOO_MANY_REQUESTS
          )
        }

        // Increment counter
        clientRecord.count++

        // Add rate limit headers
        c.header('X-RateLimit-Limit', maxRequests.toString())
        c.header('X-RateLimit-Remaining', (maxRequests - clientRecord.count).toString())
        c.header('X-RateLimit-Reset', Math.ceil(clientRecord.resetTime / 1000).toString())

        await next()
      } catch (error) {
        console.error('Rate limit middleware error:', error)
        await next()
      }
    }
  }
}

// Export singleton instance
export const authMiddleware = new AuthMiddleware()

// Helper function to get user from context
export function getAuthUser(c: Context): AuthMiddlewareContext['user'] {
  return c.get('user')
}

// Helper function to get session from context
export function getAuthSession(c: Context): AuthMiddlewareContext['session'] {
  return c.get('session')
}

// Helper function to get token from context
export function getAuthToken(c: Context): string | undefined {
  return c.get('token')
}
