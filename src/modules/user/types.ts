// User Module Types
// Type definitions for user-related operations

import { User, UserProfile, Session, UserRole, Gender } from '../../generated/prisma'

// User DTOs (Data Transfer Objects)
export interface UserDto {
  id: string
  email: string
  username: string
  firstName?: string
  lastName?: string
  avatar?: string
  role: UserRole
  isActive: boolean
  isVerified: boolean
  lastLogin?: Date
  createdAt: Date
  updatedAt: Date
}

export interface UserProfileDto {
  id: string
  userId: string
  phone?: string
  address?: string
  dateOfBirth?: Date
  gender?: Gender
  bio?: string
  website?: string
  socialLinks?: Record<string, string>
  preferences?: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

export interface UserWithProfileDto extends UserDto {
  profile?: UserProfileDto
}

export interface SessionDto {
  id: string
  userId: string
  token: string
  expiresAt: Date
  isActive: boolean
  userAgent?: string
  ipAddress?: string
  createdAt: Date
  updatedAt: Date
}

// Create/Update interfaces
export interface CreateUserData {
  email: string
  username: string
  password: string
  firstName?: string
  lastName?: string
  avatar?: string
  role?: UserRole
}

export interface UpdateUserData {
  firstName?: string
  lastName?: string
  avatar?: string
  isActive?: boolean
  isVerified?: boolean
}

export interface CreateUserProfileData {
  userId: string
  phone?: string
  address?: string
  dateOfBirth?: Date
  gender?: Gender
  bio?: string
  website?: string
  socialLinks?: Record<string, string>
  preferences?: Record<string, any>
}

export interface UpdateUserProfileData {
  phone?: string
  address?: string
  dateOfBirth?: Date
  gender?: Gender
  bio?: string
  website?: string
  socialLinks?: Record<string, string>
  preferences?: Record<string, any>
}

export interface CreateSessionData {
  userId: string
  token: string
  refreshToken?: string
  expiresAt: Date
  userAgent?: string
  ipAddress?: string
}

export interface UpdateSessionData {
  isActive?: boolean
  expiresAt?: Date
}

// Query interfaces
export interface UserQuery {
  page?: number
  limit?: number
  search?: string
  role?: UserRole
  isActive?: boolean
  isVerified?: boolean
  sortBy?: 'createdAt' | 'updatedAt' | 'email' | 'username' | 'lastLogin'
  sortOrder?: 'asc' | 'desc'
}

export interface UserSearchResult {
  users: UserWithProfileDto[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// Repository interfaces
export interface IUserRepository {
  // User CRUD operations
  create(data: CreateUserData): Promise<User>
  findById(id: string): Promise<User | null>
  findByEmail(email: string): Promise<User | null>
  findByUsername(username: string): Promise<User | null>
  findWithProfile(id: string): Promise<UserWithProfileDto | null>
  update(id: string, data: UpdateUserData): Promise<User>
  delete(id: string): Promise<void>
  
  // User queries
  findMany(query: UserQuery): Promise<UserSearchResult>
  count(filters?: Partial<UserQuery>): Promise<number>
  
  // Authentication related
  updateLastLogin(id: string): Promise<void>
  
  // Profile operations
  createProfile(data: CreateUserProfileData): Promise<UserProfile>
  updateProfile(userId: string, data: UpdateUserProfileData): Promise<UserProfile>
  deleteProfile(userId: string): Promise<void>
  
  // Session operations
  createSession(data: CreateSessionData): Promise<Session>
  findSessionByToken(token: string): Promise<Session | null>
  findUserSessions(userId: string): Promise<Session[]>
  updateSession(id: string, data: UpdateSessionData): Promise<Session>
  deleteSession(id: string): Promise<void>
  deleteUserSessions(userId: string): Promise<void>
  cleanupExpiredSessions(): Promise<number>
}

// Service interfaces
export interface IUserService {
  // User management
  createUser(data: CreateUserData): Promise<UserDto>
  getUserById(id: string): Promise<UserWithProfileDto | null>
  getUserByEmail(email: string): Promise<UserDto | null>
  getUserByUsername(username: string): Promise<UserDto | null>
  updateUser(id: string, data: UpdateUserData): Promise<UserDto>
  deleteUser(id: string): Promise<void>
  
  // User queries
  getUsers(query: UserQuery): Promise<UserSearchResult>
  searchUsers(searchTerm: string, limit?: number): Promise<UserDto[]>
  
  // Profile management
  updateProfile(userId: string, data: UpdateUserProfileData): Promise<UserProfileDto>
  getProfile(userId: string): Promise<UserProfileDto | null>
  
  // Authentication helpers
  verifyUserCredentials(email: string, password: string): Promise<UserDto | null>
  updateLastLogin(userId: string): Promise<void>
  
  // Session management
  createUserSession(userId: string, token: string, refreshToken?: string, expiresAt?: Date, userAgent?: string, ipAddress?: string): Promise<SessionDto>
  getUserSessions(userId: string): Promise<SessionDto[]>
  invalidateSession(sessionId: string): Promise<void>
  invalidateAllUserSessions(userId: string): Promise<void>
  cleanupExpiredSessions(): Promise<number>
  
  // Admin operations
  updateUserRole(userId: string, role: UserRole): Promise<UserDto>
  updateUserStatus(userId: string, isActive: boolean, isVerified?: boolean): Promise<UserDto>
  
  // Statistics
  getUserStats(): Promise<{
    total: number
    active: number
    verified: number
    byRole: Record<UserRole, number>
  }>
}

// Error types
export enum UserErrorType {
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  EMAIL_ALREADY_EXISTS = 'EMAIL_ALREADY_EXISTS',
  USERNAME_ALREADY_EXISTS = 'USERNAME_ALREADY_EXISTS',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  USER_INACTIVE = 'USER_INACTIVE',
  USER_NOT_VERIFIED = 'USER_NOT_VERIFIED',
  PROFILE_NOT_FOUND = 'PROFILE_NOT_FOUND',
  SESSION_NOT_FOUND = 'SESSION_NOT_FOUND',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS'
}

export class UserError extends Error {
  constructor(
    public type: UserErrorType,
    message: string,
    public statusCode: number = 400
  ) {
    super(message)
    this.name = 'UserError'
  }
}
