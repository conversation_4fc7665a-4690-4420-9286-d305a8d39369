// User Routes
// API endpoints for user management operations

import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { UserService } from './service'
import { authMiddleware, getAuthUser } from '../auth/middleware'
import {
  updateProfileSchema,
  getUsersQuerySchema,
  updateUserRoleSchema,
  updateUserStatusSchema,
  objectIdSchema
} from '../../core/validation/schemas'
import { ResponseHelper, HTTP_STATUS } from '../../core/utils/response'
import { UserError } from './types'

const users = new Hono()
const userService = new UserService()

/**
 * GET /users
 * Get users with pagination and filtering (Admin/Moderator only)
 */
users.get(
  '/',
  authMiddleware.authenticate(),
  authMiddleware.requireModerator(),
  zValidator('query', getUsersQuerySchema),
  async (c) => {
    try {
      const query = c.req.valid('query')
      const result = await userService.getUsers(query)

      return c.json(
        ResponseHelper.paginated(
          result.users.map(user => ({
            id: user.id,
            email: user.email,
            username: user.username,
            firstName: user.firstName,
            lastName: user.lastName,
            avatar: user.avatar,
            role: user.role,
            isActive: user.isActive,
            isVerified: user.isVerified,
            lastLogin: user.lastLogin,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            profile: user.profile ? {
              phone: user.profile.phone,
              address: user.profile.address,
              dateOfBirth: user.profile.dateOfBirth,
              gender: user.profile.gender
            } : null
          })),
          result.page,
          result.limit,
          result.total,
          'Users retrieved successfully'
        ),
        HTTP_STATUS.OK
      )
    } catch (error) {
      console.error('Get users error:', error)
      return c.json(
        ResponseHelper.error('Failed to get users', 'USERS_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * GET /users/search
 * Search users (Admin/Moderator only)
 */
users.get(
  '/search',
  authMiddleware.authenticate(),
  authMiddleware.requireModerator(),
  async (c) => {
    try {
      const searchTerm = c.req.query('q') || ''
      const limit = parseInt(c.req.query('limit') || '10')

      if (!searchTerm) {
        return c.json(
          ResponseHelper.error('Search term is required', 'VALIDATION_ERROR'),
          HTTP_STATUS.BAD_REQUEST
        )
      }

      const users = await userService.searchUsers(searchTerm, limit)

      return c.json(
        ResponseHelper.success(
          users.map(user => ({
            id: user.id,
            email: user.email,
            username: user.username,
            firstName: user.firstName,
            lastName: user.lastName,
            avatar: user.avatar,
            role: user.role,
            isActive: user.isActive,
            isVerified: user.isVerified
          })),
          'Search results retrieved successfully'
        ),
        HTTP_STATUS.OK
      )
    } catch (error) {
      console.error('Search users error:', error)
      return c.json(
        ResponseHelper.error('Failed to search users', 'SEARCH_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * GET /users/stats
 * Get user statistics (Admin only)
 */
users.get(
  '/stats',
  authMiddleware.authenticate(),
  authMiddleware.requireAdmin(),
  async (c) => {
    try {
      const stats = await userService.getUserStats()

      return c.json(
        ResponseHelper.success(stats, 'User statistics retrieved successfully'),
        HTTP_STATUS.OK
      )
    } catch (error) {
      console.error('Get user stats error:', error)
      return c.json(
        ResponseHelper.error('Failed to get user statistics', 'STATS_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * GET /users/:id
 * Get user by ID (Admin/Moderator or own profile)
 */
users.get(
  '/:id',
  authMiddleware.authenticate(),
  zValidator('param', { id: objectIdSchema }),
  async (c) => {
    try {
      const { id } = c.req.valid('param')
      const currentUser = getAuthUser(c)

      // Check if user can access this profile
      if (currentUser.role !== 'ADMIN' && 
          currentUser.role !== 'MODERATOR' && 
          currentUser.id !== id) {
        return c.json(
          ResponseHelper.forbidden('Access denied'),
          HTTP_STATUS.FORBIDDEN
        )
      }

      const user = await userService.getUserById(id)

      if (!user) {
        return c.json(
          ResponseHelper.notFound('User'),
          HTTP_STATUS.NOT_FOUND
        )
      }

      // Return different data based on access level
      const isOwnProfile = currentUser.id === id
      const isAdmin = currentUser.role === 'ADMIN' || currentUser.role === 'MODERATOR'

      const responseData = {
        id: user.id,
        email: isOwnProfile || isAdmin ? user.email : undefined,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        role: isAdmin ? user.role : undefined,
        isActive: isAdmin ? user.isActive : undefined,
        isVerified: isAdmin ? user.isVerified : undefined,
        lastLogin: isOwnProfile || isAdmin ? user.lastLogin : undefined,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        profile: user.profile
      }

      return c.json(
        ResponseHelper.success(responseData, 'User retrieved successfully'),
        HTTP_STATUS.OK
      )
    } catch (error) {
      console.error('Get user error:', error)
      return c.json(
        ResponseHelper.error('Failed to get user', 'USER_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * PUT /users/:id/profile
 * Update user profile (own profile only)
 */
users.put(
  '/:id/profile',
  authMiddleware.authenticate(),
  authMiddleware.requireOwnershipOrAdmin('id'),
  zValidator('param', { id: objectIdSchema }),
  zValidator('json', updateProfileSchema),
  async (c) => {
    try {
      const { id } = c.req.valid('param')
      const data = c.req.valid('json')

      const profile = await userService.updateProfile(id, data)

      return c.json(
        ResponseHelper.updated(profile, 'Profile updated successfully'),
        HTTP_STATUS.OK
      )
    } catch (error) {
      if (error instanceof UserError) {
        return c.json(
          ResponseHelper.error(error.message, error.type, null, error.statusCode),
          error.statusCode
        )
      }

      console.error('Update profile error:', error)
      return c.json(
        ResponseHelper.error('Failed to update profile', 'PROFILE_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * GET /users/:id/profile
 * Get user profile (own profile or admin)
 */
users.get(
  '/:id/profile',
  authMiddleware.authenticate(),
  authMiddleware.requireOwnershipOrAdmin('id'),
  zValidator('param', { id: objectIdSchema }),
  async (c) => {
    try {
      const { id } = c.req.valid('param')
      const profile = await userService.getProfile(id)

      if (!profile) {
        return c.json(
          ResponseHelper.notFound('Profile'),
          HTTP_STATUS.NOT_FOUND
        )
      }

      return c.json(
        ResponseHelper.success(profile, 'Profile retrieved successfully'),
        HTTP_STATUS.OK
      )
    } catch (error) {
      console.error('Get profile error:', error)
      return c.json(
        ResponseHelper.error('Failed to get profile', 'PROFILE_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * PUT /users/:id/role
 * Update user role (Admin only)
 */
users.put(
  '/:id/role',
  authMiddleware.authenticate(),
  authMiddleware.requireAdmin(),
  zValidator('param', { id: objectIdSchema }),
  zValidator('json', updateUserRoleSchema.omit({ userId: true })),
  async (c) => {
    try {
      const { id } = c.req.valid('param')
      const { role } = c.req.valid('json')
      const currentUser = getAuthUser(c)

      // Prevent self-role modification
      if (currentUser.id === id) {
        return c.json(
          ResponseHelper.error('Cannot modify your own role', 'SELF_MODIFICATION_ERROR'),
          HTTP_STATUS.BAD_REQUEST
        )
      }

      const user = await userService.updateUserRole(id, role)

      return c.json(
        ResponseHelper.updated(
          {
            id: user.id,
            email: user.email,
            username: user.username,
            role: user.role,
            isActive: user.isActive,
            isVerified: user.isVerified,
            updatedAt: user.updatedAt
          },
          'User role updated successfully'
        ),
        HTTP_STATUS.OK
      )
    } catch (error) {
      if (error instanceof UserError) {
        return c.json(
          ResponseHelper.error(error.message, error.type, null, error.statusCode),
          error.statusCode
        )
      }

      console.error('Update user role error:', error)
      return c.json(
        ResponseHelper.error('Failed to update user role', 'ROLE_UPDATE_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * PUT /users/:id/status
 * Update user status (Admin only)
 */
users.put(
  '/:id/status',
  authMiddleware.authenticate(),
  authMiddleware.requireAdmin(),
  zValidator('param', { id: objectIdSchema }),
  zValidator('json', updateUserStatusSchema.omit({ userId: true })),
  async (c) => {
    try {
      const { id } = c.req.valid('param')
      const { isActive, isVerified } = c.req.valid('json')
      const currentUser = getAuthUser(c)

      // Prevent self-status modification
      if (currentUser.id === id) {
        return c.json(
          ResponseHelper.error('Cannot modify your own status', 'SELF_MODIFICATION_ERROR'),
          HTTP_STATUS.BAD_REQUEST
        )
      }

      const user = await userService.updateUserStatus(id, isActive, isVerified)

      return c.json(
        ResponseHelper.updated(
          {
            id: user.id,
            email: user.email,
            username: user.username,
            role: user.role,
            isActive: user.isActive,
            isVerified: user.isVerified,
            updatedAt: user.updatedAt
          },
          'User status updated successfully'
        ),
        HTTP_STATUS.OK
      )
    } catch (error) {
      if (error instanceof UserError) {
        return c.json(
          ResponseHelper.error(error.message, error.type, null, error.statusCode),
          error.statusCode
        )
      }

      console.error('Update user status error:', error)
      return c.json(
        ResponseHelper.error('Failed to update user status', 'STATUS_UPDATE_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * DELETE /users/:id
 * Delete user (Admin only)
 */
users.delete(
  '/:id',
  authMiddleware.authenticate(),
  authMiddleware.requireAdmin(),
  zValidator('param', { id: objectIdSchema }),
  async (c) => {
    try {
      const { id } = c.req.valid('param')
      const currentUser = getAuthUser(c)

      // Prevent self-deletion
      if (currentUser.id === id) {
        return c.json(
          ResponseHelper.error('Cannot delete your own account', 'SELF_DELETION_ERROR'),
          HTTP_STATUS.BAD_REQUEST
        )
      }

      await userService.deleteUser(id)

      return c.json(
        ResponseHelper.deleted('User deleted successfully'),
        HTTP_STATUS.OK
      )
    } catch (error) {
      if (error instanceof UserError) {
        return c.json(
          ResponseHelper.error(error.message, error.type, null, error.statusCode),
          error.statusCode
        )
      }

      console.error('Delete user error:', error)
      return c.json(
        ResponseHelper.error('Failed to delete user', 'DELETE_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * GET /users/:id/sessions
 * Get user sessions (own sessions or admin)
 */
users.get(
  '/:id/sessions',
  authMiddleware.authenticate(),
  authMiddleware.requireOwnershipOrAdmin('id'),
  zValidator('param', { id: objectIdSchema }),
  async (c) => {
    try {
      const { id } = c.req.valid('param')
      const sessions = await userService.getUserSessions(id)

      return c.json(
        ResponseHelper.success(
          sessions.map(session => ({
            id: session.id,
            expiresAt: session.expiresAt,
            isActive: session.isActive,
            userAgent: session.userAgent,
            ipAddress: session.ipAddress,
            createdAt: session.createdAt,
            updatedAt: session.updatedAt
          })),
          'User sessions retrieved successfully'
        ),
        HTTP_STATUS.OK
      )
    } catch (error) {
      console.error('Get user sessions error:', error)
      return c.json(
        ResponseHelper.error('Failed to get user sessions', 'SESSIONS_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

/**
 * DELETE /users/:id/sessions
 * Invalidate all user sessions (own sessions or admin)
 */
users.delete(
  '/:id/sessions',
  authMiddleware.authenticate(),
  authMiddleware.requireOwnershipOrAdmin('id'),
  zValidator('param', { id: objectIdSchema }),
  async (c) => {
    try {
      const { id } = c.req.valid('param')
      await userService.invalidateAllUserSessions(id)

      return c.json(
        ResponseHelper.success(null, 'All user sessions invalidated successfully'),
        HTTP_STATUS.OK
      )
    } catch (error) {
      console.error('Invalidate user sessions error:', error)
      return c.json(
        ResponseHelper.error('Failed to invalidate user sessions', 'SESSIONS_ERROR'),
        HTTP_STATUS.INTERNAL_SERVER_ERROR
      )
    }
  }
)

export { users }
