// User Service
// Business logic for user management operations

import { UserRole } from '../../generated/prisma'
import { UserRepository } from './repository'
import { passwordManager } from '../../core/auth/password'
import {
  IUserService,
  UserDto,
  UserWithProfileDto,
  UserProfileDto,
  SessionDto,
  CreateUserData,
  UpdateUserData,
  UpdateUserProfileData,
  UserQuery,
  UserSearchResult,
  UserError,
  UserErrorType
} from './types'

export class UserService implements IUserService {
  private userRepository: UserRepository

  constructor() {
    this.userRepository = new UserRepository()
  }

  /**
   * Create a new user
   */
  async createUser(data: CreateUserData): Promise<UserDto> {
    try {
      // Hash password if provided
      if (data.password) {
        data.password = await passwordManager.hashPassword(data.password)
      }

      const user = await this.userRepository.create(data)
      
      return {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        isVerified: user.isVerified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    } catch (error) {
      if (error instanceof UserError) {
        throw error
      }
      console.error('Error creating user:', error)
      throw new UserError(
        UserErrorType.USER_NOT_FOUND,
        'Failed to create user',
        500
      )
    }
  }

  /**
   * Get user by ID with profile
   */
  async getUserById(id: string): Promise<UserWithProfileDto | null> {
    try {
      return await this.userRepository.findWithProfile(id)
    } catch (error) {
      console.error('Error getting user by ID:', error)
      return null
    }
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email: string): Promise<UserDto | null> {
    try {
      const user = await this.userRepository.findByEmail(email)
      if (!user) return null

      return {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        isVerified: user.isVerified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    } catch (error) {
      console.error('Error getting user by email:', error)
      return null
    }
  }

  /**
   * Get user by username
   */
  async getUserByUsername(username: string): Promise<UserDto | null> {
    try {
      const user = await this.userRepository.findByUsername(username)
      if (!user) return null

      return {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        isVerified: user.isVerified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    } catch (error) {
      console.error('Error getting user by username:', error)
      return null
    }
  }

  /**
   * Update user
   */
  async updateUser(id: string, data: UpdateUserData): Promise<UserDto> {
    try {
      const user = await this.userRepository.update(id, data)
      
      return {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        isVerified: user.isVerified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    } catch (error) {
      if (error instanceof UserError) {
        throw error
      }
      console.error('Error updating user:', error)
      throw new UserError(
        UserErrorType.USER_NOT_FOUND,
        'Failed to update user',
        500
      )
    }
  }

  /**
   * Delete user
   */
  async deleteUser(id: string): Promise<void> {
    try {
      // Delete user profile first
      await this.userRepository.deleteProfile(id)
      
      // Delete user sessions
      await this.userRepository.deleteUserSessions(id)
      
      // Delete user
      await this.userRepository.delete(id)
    } catch (error) {
      if (error instanceof UserError) {
        throw error
      }
      console.error('Error deleting user:', error)
      throw new UserError(
        UserErrorType.USER_NOT_FOUND,
        'Failed to delete user',
        500
      )
    }
  }

  /**
   * Get users with pagination and filtering
   */
  async getUsers(query: UserQuery): Promise<UserSearchResult> {
    try {
      return await this.userRepository.findMany(query)
    } catch (error) {
      console.error('Error getting users:', error)
      return {
        users: [],
        total: 0,
        page: query.page || 1,
        limit: query.limit || 10,
        totalPages: 0
      }
    }
  }

  /**
   * Search users by term
   */
  async searchUsers(searchTerm: string, limit: number = 10): Promise<UserDto[]> {
    try {
      const result = await this.userRepository.findMany({
        search: searchTerm,
        limit,
        page: 1
      })

      return result.users.map(user => ({
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        isVerified: user.isVerified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }))
    } catch (error) {
      console.error('Error searching users:', error)
      return []
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(userId: string, data: UpdateUserProfileData): Promise<UserProfileDto> {
    try {
      const profile = await this.userRepository.updateProfile(userId, data)
      
      return {
        id: profile.id,
        userId: profile.userId,
        phone: profile.phone,
        address: profile.address,
        dateOfBirth: profile.dateOfBirth,
        gender: profile.gender,
        bio: profile.bio,
        website: profile.website,
        socialLinks: profile.socialLinks as Record<string, string>,
        preferences: profile.preferences as Record<string, any>,
        createdAt: profile.createdAt,
        updatedAt: profile.updatedAt
      }
    } catch (error) {
      console.error('Error updating profile:', error)
      throw new UserError(
        UserErrorType.PROFILE_NOT_FOUND,
        'Failed to update profile',
        500
      )
    }
  }

  /**
   * Get user profile
   */
  async getProfile(userId: string): Promise<UserProfileDto | null> {
    try {
      const userWithProfile = await this.userRepository.findWithProfile(userId)
      return userWithProfile?.profile || null
    } catch (error) {
      console.error('Error getting profile:', error)
      return null
    }
  }

  /**
   * Verify user credentials
   */
  async verifyUserCredentials(email: string, password: string): Promise<UserDto | null> {
    try {
      const user = await this.userRepository.findByEmail(email)
      if (!user) return null

      const isPasswordValid = await passwordManager.verifyPassword(password, user.password)
      if (!isPasswordValid) return null

      return {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        isVerified: user.isVerified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    } catch (error) {
      console.error('Error verifying credentials:', error)
      return null
    }
  }

  /**
   * Update last login timestamp
   */
  async updateLastLogin(userId: string): Promise<void> {
    try {
      await this.userRepository.updateLastLogin(userId)
    } catch (error) {
      console.error('Error updating last login:', error)
      // Don't throw error for this operation
    }
  }

  /**
   * Create user session
   */
  async createUserSession(
    userId: string,
    token: string,
    refreshToken?: string,
    expiresAt?: Date,
    userAgent?: string,
    ipAddress?: string
  ): Promise<SessionDto> {
    try {
      const session = await this.userRepository.createSession({
        userId,
        token,
        refreshToken,
        expiresAt: expiresAt || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days default
        userAgent,
        ipAddress
      })

      return {
        id: session.id,
        userId: session.userId,
        token: session.token,
        expiresAt: session.expiresAt,
        isActive: session.isActive,
        userAgent: session.userAgent,
        ipAddress: session.ipAddress,
        createdAt: session.createdAt,
        updatedAt: session.updatedAt
      }
    } catch (error) {
      console.error('Error creating user session:', error)
      throw new UserError(
        UserErrorType.SESSION_NOT_FOUND,
        'Failed to create session',
        500
      )
    }
  }

  /**
   * Get user sessions
   */
  async getUserSessions(userId: string): Promise<SessionDto[]> {
    try {
      const sessions = await this.userRepository.findUserSessions(userId)
      return sessions.map(session => ({
        id: session.id,
        userId: session.userId,
        token: session.token,
        expiresAt: session.expiresAt,
        isActive: session.isActive,
        userAgent: session.userAgent,
        ipAddress: session.ipAddress,
        createdAt: session.createdAt,
        updatedAt: session.updatedAt
      }))
    } catch (error) {
      console.error('Error getting user sessions:', error)
      return []
    }
  }

  /**
   * Invalidate session
   */
  async invalidateSession(sessionId: string): Promise<void> {
    try {
      await this.userRepository.deleteSession(sessionId)
    } catch (error) {
      console.error('Error invalidating session:', error)
      throw new UserError(
        UserErrorType.SESSION_NOT_FOUND,
        'Failed to invalidate session',
        500
      )
    }
  }

  /**
   * Invalidate all user sessions
   */
  async invalidateAllUserSessions(userId: string): Promise<void> {
    try {
      await this.userRepository.deleteUserSessions(userId)
    } catch (error) {
      console.error('Error invalidating all user sessions:', error)
      throw new UserError(
        UserErrorType.SESSION_NOT_FOUND,
        'Failed to invalidate sessions',
        500
      )
    }
  }

  /**
   * Cleanup expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    try {
      return await this.userRepository.cleanupExpiredSessions()
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error)
      return 0
    }
  }

  /**
   * Update user role (Admin operation)
   */
  async updateUserRole(userId: string, role: UserRole): Promise<UserDto> {
    try {
      const user = await this.userRepository.update(userId, { role })
      
      return {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        isVerified: user.isVerified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    } catch (error) {
      if (error instanceof UserError) {
        throw error
      }
      console.error('Error updating user role:', error)
      throw new UserError(
        UserErrorType.USER_NOT_FOUND,
        'Failed to update user role',
        500
      )
    }
  }

  /**
   * Update user status (Admin operation)
   */
  async updateUserStatus(userId: string, isActive: boolean, isVerified?: boolean): Promise<UserDto> {
    try {
      const updateData: UpdateUserData = { isActive }
      if (isVerified !== undefined) {
        updateData.isVerified = isVerified
      }

      const user = await this.userRepository.update(userId, updateData)
      
      return {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        isVerified: user.isVerified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    } catch (error) {
      if (error instanceof UserError) {
        throw error
      }
      console.error('Error updating user status:', error)
      throw new UserError(
        UserErrorType.USER_NOT_FOUND,
        'Failed to update user status',
        500
      )
    }
  }

  /**
   * Get user statistics
   */
  async getUserStats(): Promise<{
    total: number
    active: number
    verified: number
    byRole: Record<UserRole, number>
  }> {
    try {
      const [total, active, verified] = await Promise.all([
        this.userRepository.count(),
        this.userRepository.count({ isActive: true }),
        this.userRepository.count({ isVerified: true })
      ])

      const byRole: Record<UserRole, number> = {
        [UserRole.ADMIN]: await this.userRepository.count({ role: UserRole.ADMIN }),
        [UserRole.MODERATOR]: await this.userRepository.count({ role: UserRole.MODERATOR }),
        [UserRole.USER]: await this.userRepository.count({ role: UserRole.USER }),
        [UserRole.GUEST]: await this.userRepository.count({ role: UserRole.GUEST })
      }

      return {
        total,
        active,
        verified,
        byRole
      }
    } catch (error) {
      console.error('Error getting user stats:', error)
      return {
        total: 0,
        active: 0,
        verified: 0,
        byRole: {
          [UserRole.ADMIN]: 0,
          [UserRole.MODERATOR]: 0,
          [UserRole.USER]: 0,
          [UserRole.GUEST]: 0
        }
      }
    }
  }
}
