// User Repository
// Data access layer for user operations

import { User, UserProfile, Session, UserRole, Prisma } from '../../generated/prisma'
import { db } from '../../core/database'
import {
  IUserRepository,
  CreateUserData,
  UpdateUserData,
  CreateUserProfileData,
  UpdateUserProfileData,
  CreateSessionData,
  UpdateSessionData,
  UserQuery,
  UserSearchResult,
  UserWithProfileDto,
  UserError,
  UserErrorType
} from './types'

export class UserRepository implements IUserRepository {
  // User CRUD operations
  async create(data: CreateUserData): Promise<User> {
    try {
      // Check if email already exists
      const existingEmail = await db.user.findUnique({
        where: { email: data.email }
      })
      
      if (existingEmail) {
        throw new UserError(
          UserErrorType.EMAIL_ALREADY_EXISTS,
          'Email already exists',
          409
        )
      }

      // Check if username already exists
      const existingUsername = await db.user.findUnique({
        where: { username: data.username }
      })
      
      if (existingUsername) {
        throw new UserError(
          UserErrorType.USERNAME_ALREADY_EXISTS,
          'Username already exists',
          409
        )
      }

      return await db.user.create({
        data: {
          ...data,
          role: data.role || UserRole.USER
        }
      })
    } catch (error) {
      if (error instanceof UserError) {
        throw error
      }
      console.error('Error creating user:', error)
      throw new Error('Failed to create user')
    }
  }

  async findById(id: string): Promise<User | null> {
    try {
      return await db.user.findUnique({
        where: { id }
      })
    } catch (error) {
      console.error('Error finding user by ID:', error)
      return null
    }
  }

  async findByEmail(email: string): Promise<User | null> {
    try {
      return await db.user.findUnique({
        where: { email }
      })
    } catch (error) {
      console.error('Error finding user by email:', error)
      return null
    }
  }

  async findByUsername(username: string): Promise<User | null> {
    try {
      return await db.user.findUnique({
        where: { username }
      })
    } catch (error) {
      console.error('Error finding user by username:', error)
      return null
    }
  }

  async findWithProfile(id: string): Promise<UserWithProfileDto | null> {
    try {
      const user = await db.user.findUnique({
        where: { id },
        include: {
          profile: true
        }
      })

      if (!user) return null

      return {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        isVerified: user.isVerified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        profile: user.profile ? {
          id: user.profile.id,
          userId: user.profile.userId,
          phone: user.profile.phone,
          address: user.profile.address,
          dateOfBirth: user.profile.dateOfBirth,
          gender: user.profile.gender,
          bio: user.profile.bio,
          website: user.profile.website,
          socialLinks: user.profile.socialLinks as Record<string, string>,
          preferences: user.profile.preferences as Record<string, any>,
          createdAt: user.profile.createdAt,
          updatedAt: user.profile.updatedAt
        } : undefined
      }
    } catch (error) {
      console.error('Error finding user with profile:', error)
      return null
    }
  }

  async update(id: string, data: UpdateUserData): Promise<User> {
    try {
      const user = await db.user.update({
        where: { id },
        data
      })
      return user
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new UserError(
            UserErrorType.USER_NOT_FOUND,
            'User not found',
            404
          )
        }
      }
      console.error('Error updating user:', error)
      throw new Error('Failed to update user')
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await db.user.delete({
        where: { id }
      })
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new UserError(
            UserErrorType.USER_NOT_FOUND,
            'User not found',
            404
          )
        }
      }
      console.error('Error deleting user:', error)
      throw new Error('Failed to delete user')
    }
  }

  async findMany(query: UserQuery): Promise<UserSearchResult> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        role,
        isActive,
        isVerified,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = query

      const skip = (page - 1) * limit
      
      // Build where clause
      const where: Prisma.UserWhereInput = {}
      
      if (search) {
        where.OR = [
          { email: { contains: search, mode: 'insensitive' } },
          { username: { contains: search, mode: 'insensitive' } },
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } }
        ]
      }
      
      if (role !== undefined) where.role = role
      if (isActive !== undefined) where.isActive = isActive
      if (isVerified !== undefined) where.isVerified = isVerified

      // Get total count
      const total = await db.user.count({ where })

      // Get users with profiles
      const users = await db.user.findMany({
        where,
        include: {
          profile: true
        },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder
        }
      })

      const userDtos: UserWithProfileDto[] = users.map(user => ({
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        avatar: user.avatar,
        role: user.role,
        isActive: user.isActive,
        isVerified: user.isVerified,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        profile: user.profile ? {
          id: user.profile.id,
          userId: user.profile.userId,
          phone: user.profile.phone,
          address: user.profile.address,
          dateOfBirth: user.profile.dateOfBirth,
          gender: user.profile.gender,
          bio: user.profile.bio,
          website: user.profile.website,
          socialLinks: user.profile.socialLinks as Record<string, string>,
          preferences: user.profile.preferences as Record<string, any>,
          createdAt: user.profile.createdAt,
          updatedAt: user.profile.updatedAt
        } : undefined
      }))

      return {
        users: userDtos,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    } catch (error) {
      console.error('Error finding users:', error)
      throw new Error('Failed to find users')
    }
  }

  async count(filters?: Partial<UserQuery>): Promise<number> {
    try {
      const where: Prisma.UserWhereInput = {}
      
      if (filters?.role !== undefined) where.role = filters.role
      if (filters?.isActive !== undefined) where.isActive = filters.isActive
      if (filters?.isVerified !== undefined) where.isVerified = filters.isVerified

      return await db.user.count({ where })
    } catch (error) {
      console.error('Error counting users:', error)
      return 0
    }
  }

  async updateLastLogin(id: string): Promise<void> {
    try {
      await db.user.update({
        where: { id },
        data: { lastLogin: new Date() }
      })
    } catch (error) {
      console.error('Error updating last login:', error)
      // Don't throw error for this operation
    }
  }

  // Profile operations
  async createProfile(data: CreateUserProfileData): Promise<UserProfile> {
    try {
      return await db.userProfile.create({
        data: {
          ...data,
          socialLinks: data.socialLinks || {},
          preferences: data.preferences || {}
        }
      })
    } catch (error) {
      console.error('Error creating user profile:', error)
      throw new Error('Failed to create user profile')
    }
  }

  async updateProfile(userId: string, data: UpdateUserProfileData): Promise<UserProfile> {
    try {
      return await db.userProfile.upsert({
        where: { userId },
        update: data,
        create: {
          userId,
          ...data,
          socialLinks: data.socialLinks || {},
          preferences: data.preferences || {}
        }
      })
    } catch (error) {
      console.error('Error updating user profile:', error)
      throw new Error('Failed to update user profile')
    }
  }

  async deleteProfile(userId: string): Promise<void> {
    try {
      await db.userProfile.delete({
        where: { userId }
      })
    } catch (error) {
      console.error('Error deleting user profile:', error)
      // Don't throw error if profile doesn't exist
    }
  }

  // Session operations
  async createSession(data: CreateSessionData): Promise<Session> {
    try {
      return await db.session.create({
        data
      })
    } catch (error) {
      console.error('Error creating session:', error)
      throw new Error('Failed to create session')
    }
  }

  async findSessionByToken(token: string): Promise<Session | null> {
    try {
      return await db.session.findUnique({
        where: { token }
      })
    } catch (error) {
      console.error('Error finding session by token:', error)
      return null
    }
  }

  async findUserSessions(userId: string): Promise<Session[]> {
    try {
      return await db.session.findMany({
        where: {
          userId,
          isActive: true,
          expiresAt: {
            gt: new Date()
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      })
    } catch (error) {
      console.error('Error finding user sessions:', error)
      return []
    }
  }

  async updateSession(id: string, data: UpdateSessionData): Promise<Session> {
    try {
      return await db.session.update({
        where: { id },
        data
      })
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new UserError(
            UserErrorType.SESSION_NOT_FOUND,
            'Session not found',
            404
          )
        }
      }
      console.error('Error updating session:', error)
      throw new Error('Failed to update session')
    }
  }

  async deleteSession(id: string): Promise<void> {
    try {
      await db.session.delete({
        where: { id }
      })
    } catch (error) {
      console.error('Error deleting session:', error)
      // Don't throw error if session doesn't exist
    }
  }

  async deleteUserSessions(userId: string): Promise<void> {
    try {
      await db.session.deleteMany({
        where: { userId }
      })
    } catch (error) {
      console.error('Error deleting user sessions:', error)
      throw new Error('Failed to delete user sessions')
    }
  }

  async cleanupExpiredSessions(): Promise<number> {
    try {
      const result = await db.session.deleteMany({
        where: {
          OR: [
            { expiresAt: { lt: new Date() } },
            { isActive: false }
          ]
        }
      })
      return result.count
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error)
      return 0
    }
  }
}
