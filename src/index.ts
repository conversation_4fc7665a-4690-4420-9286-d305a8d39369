
import { serve } from 'bun'
import { config } from 'dotenv'
import app from './core/app'
import { webSocketService } from '@/core/services/websocket'

// Load environment variables
config()

const port = parseInt(process.env.PORT || '3001')
const wsPort = parseInt(process.env.WS_PORT || '3002')

console.log(`🚀 Server starting on port ${port}...`)

// Start WebSocket server
webSocketService.init(wsPort)

serve({
  fetch: app.fetch,
  port
})

console.log(`✅ HTTP Server running on http://localhost:${port}`)
console.log(`🌐 WebSocket Server running on ws://localhost:${wsPort}`)
console.log(`📚 API Documentation:`)
console.log(`   - Health check: GET /health`)
console.log(`   - User routes: /api/v1/users`)
console.log(`   - Book routes: /api/v1/books`)
console.log(``)
console.log(`🔐 Authentication Endpoints:`)
console.log(`   - Register: POST /api/v1/users/register`)
console.log(`   - Verify Email: POST /api/v1/users/verify-email`)
console.log(`   - Login: POST /api/v1/users/login`)
console.log(`   - Google OAuth URL: GET /api/v1/users/auth/google/url`)
console.log(`   - Google Login: POST /api/v1/users/auth/google`)
console.log(`   - Google Callback: POST /api/v1/users/auth/google/callback`)
console.log(`   - Forgot Password: POST /api/v1/users/forgot-password`)
console.log(`   - Reset Password: POST /api/v1/users/reset-password`)
console.log(``)
console.log(`👤 Profile Management:`)
console.log(`   - Get Profile: GET /api/v1/users/profile`)
console.log(`   - Update Profile: PUT /api/v1/users/profile`)
console.log(`   - Change Password: POST /api/v1/users/change-password`)
console.log(`   - Upload Picture: POST /api/v1/users/profile/picture`)
console.log(`   - Delete Account: DELETE /api/v1/users/profile`)
console.log(``)
console.log(`🔒 Two-Factor Authentication:`)
console.log(`   - Setup 2FA: POST /api/v1/users/2fa/setup`)
console.log(`   - Verify 2FA: POST /api/v1/users/2fa/verify`)
console.log(`   - Disable 2FA: POST /api/v1/users/2fa/disable`)
console.log(``)
console.log(`⚙️ Settings:`)
console.log(`   - Privacy Settings: PUT /api/v1/users/settings/privacy`)
console.log(`   - Notification Settings: PUT /api/v1/users/settings/notifications`)
console.log(``)
console.log(`👥 Social Features:`)
console.log(`   - Referral Stats: GET /api/v1/users/referrals`)
console.log(`   - Send Invites: POST /api/v1/users/invites`)
console.log(``)
console.log(`👑 Admin Features:`)
console.log(`   - User Analytics: GET /api/v1/users/analytics`)
console.log(`   - All Users: GET /api/v1/users`)
console.log(`   - User by ID: GET /api/v1/users/:id`)
console.log(`   - Deactivate User: DELETE /api/v1/users/:id`)
console.log(``)
console.log(`📊 Analytics & Monitoring:`)
console.log(`   - Real-time Analytics: GET /api/v1/admin/analytics/realtime`)
console.log(`   - User Analytics: GET /api/v1/admin/analytics/users`)
console.log(`   - Performance Analytics: GET /api/v1/admin/analytics/performance`)
console.log(``)
console.log(`🔒 Security Management:`)
console.log(`   - Rate Limits: GET /api/v1/admin/security/rate-limits`)
console.log(`   - Reset Rate Limit: DELETE /api/v1/admin/security/rate-limits/:ip`)
console.log(`   - Suspicious IPs: GET /api/v1/admin/security/suspicious`)
console.log(`   - Clear Suspicious IP: DELETE /api/v1/admin/security/suspicious/:ip`)
console.log(``)
console.log(`🌐 WebSocket Features:`)
console.log(`   - Real-time notifications`)
console.log(`   - Live chat rooms`)
console.log(`   - User presence tracking`)
console.log(`   - System broadcasts`) 

