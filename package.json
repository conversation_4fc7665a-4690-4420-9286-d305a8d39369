{"name": "backend", "scripts": {"dev": "bun run --hot src/index.ts", "start": "bun run src/index.ts", "build": "bun build src/index.ts --outdir ./dist", "db:generate": "bunx prisma generate --schema=./prisma/schema1.prisma", "db:push": "bunx prisma db push --schema=./prisma/schema1.prisma", "db:studio": "bunx prisma studio --schema=./prisma/schema1.prisma"}, "dependencies": {"@hono/zod-validator": "^0.7.0", "@prisma/client": "^6.11.0", "bcryptjs": "^3.0.2", "hono": "^4.8.3", "jsonwebtoken": "^9.0.2", "prisma": "^6.11.0", "zod": "^3.25.67"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/bun": "latest", "@types/jsonwebtoken": "^9.0.10", "dotenv": "^17.0.1"}}