# WebShop Backend - ระบบสมาชิก

ระบบ Backend สำหรับ WebShop ที่ใช้ Bun.js, Hono, Prisma และ MongoDB พร้อมโครงสร้าง Core/Module Architecture

## 🚀 เทคโนโลยีที่ใช้

- **Runtime**: Bun.js
- **Framework**: Hono
- **Database**: MongoDB (Primary), PostgreSQL (Secondary/Future)
- **ORM**: Prisma
- **Authentication**: JWT + bcrypt
- **Validation**: Zod
- **Language**: TypeScript

## 🛠️ การติดตั้ง

1. **ติดตั้ง dependencies**
```bash
bun install
```

2. **Generate Prisma client**
```bash
bun run db:generate
```

3. **Push database schema**
```bash
bun run db:push
```

## 🚀 การรันโปรเจ็ค

### Development mode
```bash
bun run dev
```

Server จะรันที่ http://localhost:3005

### Production mode
```bash
bun run start
```

## 🔐 API Endpoints

### Authentication
- `POST /api/auth/register` - สมัครสมาชิก
- `POST /api/auth/login` - เข้าสู่ระบบ
- `POST /api/auth/logout` - ออกจากระบบ
- `GET /api/auth/me` - ข้อมูลผู้ใช้ปัจจุบัน
- `POST /api/auth/change-password` - เปลี่ยนรหัสผ่าน

### User Management
- `GET /api/users` - ดูรายชื่อผู้ใช้ (Admin/Moderator)
- `GET /api/users/:id` - ดูข้อมูลผู้ใช้
- `PUT /api/users/:id/profile` - แก้ไขโปรไฟล์
- `PUT /api/users/:id/role` - เปลี่ยนบทบาท (Admin)
- `PUT /api/users/:id/status` - เปลี่ยนสถานะ (Admin)

### System
- `GET /health` - Health check
- `GET /` - API information

## 🧪 การทดสอบ API

ใช้ไฟล์ `test-api.http` สำหรับทดสอบ API endpoints

## 🔒 User Roles

- **ADMIN**: สิทธิ์เต็ม
- **MODERATOR**: จัดการผู้ใช้
- **USER**: ผู้ใช้ทั่วไป
- **GUEST**: ผู้เยี่ยมชม
